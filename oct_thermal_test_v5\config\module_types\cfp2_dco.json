{"module_type": "cfp2_dco", "display_name": "CFP2 DCO模块", "data_fields": ["sn", "mn", "Module Temperature", "Module Power Supply", "Network TX Laser Temp", "RX Current Freq", "Current Output Power", "Current Input Power", "Network FEC Uncorr Blk Cnt"], "validation_rules": [{"field_name": "Module Temperature", "min_value": -10.0, "max_value": 85.0, "rule_type": "range", "enabled": true, "description": "模块温度范围检查"}, {"field_name": "Module Power Supply", "min_value": 3.0, "max_value": 3.6, "rule_type": "range", "enabled": true, "description": "模块电源电压范围检查"}, {"field_name": "Network TX Laser Temp", "min_value": -10.0, "max_value": 85.0, "rule_type": "range", "enabled": true, "description": "发射激光器温度范围检查"}, {"field_name": "Current Output Power", "min_value": -10.0, "max_value": 10.0, "rule_type": "range", "enabled": true, "description": "当前输出功率范围检查"}, {"field_name": "Current Input Power", "min_value": -20.0, "max_value": 5.0, "rule_type": "range", "enabled": true, "description": "当前输入功率范围检查"}, {"field_name": "Network FEC Uncorr Blk Cnt", "expected_value": 0, "rule_type": "increase", "enabled": true, "description": "网络FEC不可纠正块计数递增检查"}], "pre_actions": ["switch_to_slot", "check_module_presence"], "post_actions": ["save_data_to_excel"], "enabled": true, "description": "CFP2 DCO模块配置，用于高速光通信测试"}