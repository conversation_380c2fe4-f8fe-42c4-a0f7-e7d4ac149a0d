#!/usr/bin/env python3
"""
基础功能测试
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

def test_imports():
    """测试基础导入"""
    print("测试基础导入...")
    
    try:
        # 测试基础模块
        from src.utils.logger import get_logger
        print("✓ logger导入成功")
        
        from src.devices.base import DeviceConnection, Device, DeviceManager
        print("✓ base设备类导入成功")
        
        from src.devices.ssh_connection import SSHConnection
        print("✓ SSH连接导入成功")
        
        from src.devices.oct_device import OCTDevice
        print("✓ OCT设备导入成功")
        
        from src.devices.npb_device import NPBDevice
        print("✓ NPB设备导入成功")
        
        from src.app.config_manager import ConfigManager
        print("✓ 配置管理器导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_ssh_connection():
    """测试SSH连接"""
    print("\n测试SSH连接...")
    
    try:
        from src.devices.ssh_connection import SSHConnection
        
        # 创建SSH连接实例
        ssh_conn = SSHConnection(
            connection_id="test_ssh",
            host="*************",
            username="admin",
            password="Admin_123"
        )
        
        print("✓ SSH连接实例创建成功")
        print(f"  连接信息: {ssh_conn.get_connection_info()}")
        
        return True
        
    except Exception as e:
        print(f"✗ SSH连接测试失败: {e}")
        return False

def test_config_manager():
    """测试配置管理器"""
    print("\n测试配置管理器...")
    
    try:
        from src.app.config_manager import ConfigManager
        
        # 创建配置管理器实例
        config_mgr = ConfigManager("test_config")
        
        # 测试基本配置操作
        config_mgr.set_app_config("test_key", "test_value")
        value = config_mgr.get_app_config("test_key")
        
        if value == "test_value":
            print("✓ 配置管理器基本功能正常")
        else:
            print(f"✗ 配置管理器功能异常: 期望 'test_value', 得到 '{value}'")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 配置管理器测试失败: {e}")
        return False

def test_device_creation():
    """测试设备创建"""
    print("\n测试设备创建...")
    
    try:
        from src.devices.ssh_connection import SSHConnection
        from src.devices.oct_device import OCTDevice
        from src.models.device_config import DeviceConfig, ConnectionConfig, DeviceType, ConnectionType
        
        # 创建设备配置
        device_config = DeviceConfig(
            device_id="test_oct",
            device_name="测试OCT设备",
            device_type=DeviceType.OCT,
            connection=ConnectionConfig(
                connection_type=ConnectionType.SSH,
                host="*************",
                username="admin",
                password="Admin_123"
            )
        )
        
        # 创建SSH连接
        ssh_conn = SSHConnection(
            connection_id="test_ssh",
            host=device_config.connection.host,
            username=device_config.connection.username,
            password=device_config.connection.password
        )
        
        # 创建OCT设备
        oct_device = OCTDevice(device_config, ssh_conn)
        
        print("✓ OCT设备创建成功")
        print(f"  设备状态: {oct_device.get_status()}")
        
        return True
        
    except Exception as e:
        print(f"✗ 设备创建测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("OCT Thermal Test V5 - 基础功能测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_ssh_connection,
        test_config_manager,
        test_device_creation,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有基础功能测试通过！")
        return 0
    else:
        print("✗ 部分测试失败，请检查错误信息")
        return 1

if __name__ == "__main__":
    sys.exit(main())
