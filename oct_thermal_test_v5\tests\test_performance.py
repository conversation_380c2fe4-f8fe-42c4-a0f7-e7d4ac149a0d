#!/usr/bin/env python3
"""
性能测试脚本
"""

import sys
import time
import threading
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加src目录到Python路径
current_dir = Path(__file__).parent.parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

from src.app.config_manager import ConfigManager
from src.core.data_processor import DataProcessor
from src.utils.validators import get_data_validator
from src.utils.excel_handler import ExcelHandler
from src.models.module_info import ValidationRule


class PerformanceTest:
    """性能测试类"""
    
    def __init__(self):
        self.config_manager = ConfigManager("perf_test_config")
        self.data_processor = DataProcessor(self.config_manager)
        self.data_validator = get_data_validator()
        self.excel_handler = ExcelHandler("perf_test_output")
        
        # 设置测试模块配置
        self._setup_test_config()
    
    def _setup_test_config(self):
        """设置测试配置"""
        module_config = {
            "module_type": "perf_test_module",
            "display_name": "性能测试模块",
            "data_fields": ["temperature", "voltage", "current", "power"],
            "validation_rules": [
                {
                    "field_name": "temperature",
                    "min_value": -10.0,
                    "max_value": 85.0,
                    "rule_type": "range",
                    "enabled": True,
                    "description": "温度范围检查"
                },
                {
                    "field_name": "voltage",
                    "min_value": 3.0,
                    "max_value": 3.6,
                    "rule_type": "range",
                    "enabled": True,
                    "description": "电压范围检查"
                }
            ],
            "enabled": True
        }
        
        self.config_manager.set_module_type_config("perf_test_module", module_config)
    
    def test_data_validation_performance(self, num_validations=10000):
        """测试数据验证性能"""
        print(f"测试数据验证性能 ({num_validations} 次验证)...")
        
        # 创建验证规则
        rules = [
            ValidationRule(
                field_name="temperature",
                min_value=-10.0,
                max_value=85.0,
                rule_type="range"
            ),
            ValidationRule(
                field_name="voltage",
                min_value=3.0,
                max_value=3.6,
                rule_type="range"
            ),
            ValidationRule(
                field_name="current",
                min_value=0.0,
                max_value=5.0,
                rule_type="range"
            )
        ]
        
        # 测试数据
        test_data = {
            "temperature": 25.0,
            "voltage": 3.3,
            "current": 2.5,
            "power": 8.25
        }
        
        # 性能测试
        start_time = time.time()
        
        for i in range(num_validations):
            results = self.data_validator.validate_data_dict(test_data, rules)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"  完成 {num_validations} 次验证")
        print(f"  总耗时: {duration:.3f} 秒")
        print(f"  平均耗时: {(duration/num_validations)*1000:.3f} 毫秒/次")
        print(f"  验证速率: {num_validations/duration:.0f} 次/秒")
        
        return duration
    
    def test_data_processing_performance(self, num_modules=100, data_points_per_module=1000):
        """测试数据处理性能"""
        print(f"测试数据处理性能 ({num_modules} 个模块, 每个 {data_points_per_module} 个数据点)...")
        
        start_time = time.time()
        
        for module_idx in range(num_modules):
            module_id = f"perf_test_device_{module_idx}_1/1/1"
            
            for point_idx in range(data_points_per_module):
                raw_data = {
                    "module_id": module_id,
                    "device_id": f"perf_test_device_{module_idx}",
                    "slot_id": "1/1/1",
                    "data_type": "perf_test_module",
                    "temperature": 25.0 + (point_idx % 10),
                    "voltage": 3.3 + (point_idx % 100) * 0.001,
                    "current": 2.5 + (point_idx % 50) * 0.01,
                    "power": 8.25 + (point_idx % 20) * 0.1
                }
                
                self.data_processor.process_module_data(module_id, raw_data)
        
        end_time = time.time()
        duration = end_time - start_time
        total_points = num_modules * data_points_per_module
        
        print(f"  处理 {total_points} 个数据点")
        print(f"  总耗时: {duration:.3f} 秒")
        print(f"  平均耗时: {(duration/total_points)*1000:.3f} 毫秒/点")
        print(f"  处理速率: {total_points/duration:.0f} 点/秒")
        
        return duration
    
    def test_excel_export_performance(self, num_modules=10, data_points_per_module=1000):
        """测试Excel导出性能"""
        print(f"测试Excel导出性能 ({num_modules} 个模块, 每个 {data_points_per_module} 个数据点)...")
        
        # 准备测试数据
        module_data = {}
        error_data = {}
        
        for module_idx in range(num_modules):
            module_id = f"excel_test_module_{module_idx}"
            
            data_list = []
            error_list = []
            
            for point_idx in range(data_points_per_module):
                data_point = {
                    "timestamp": f"2023-01-01 10:{point_idx//60:02d}:{point_idx%60:02d}",
                    "temperature": 25.0 + (point_idx % 10),
                    "voltage": 3.3 + (point_idx % 100) * 0.001,
                    "validation_passed": point_idx % 20 != 0  # 5% 失败率
                }
                
                data_list.append(data_point)
                
                if not data_point["validation_passed"]:
                    error_list.append(data_point)
            
            module_data[module_id] = data_list
            if error_list:
                error_data[module_id] = error_list
        
        # 准备汇总信息
        summary = {
            "session_id": "performance_test",
            "start_time": "2023-01-01 10:00:00",
            "end_time": "2023-01-01 11:00:00",
            "duration": "1:00:00",
            "total_modules": num_modules,
            "total_data_points": num_modules * data_points_per_module,
            "overall_result": True
        }
        
        # 性能测试
        start_time = time.time()
        
        file_path = self.excel_handler.create_test_report(
            "performance_test",
            module_data,
            error_data,
            summary
        )
        
        end_time = time.time()
        duration = end_time - start_time
        total_points = num_modules * data_points_per_module
        
        print(f"  导出 {total_points} 个数据点到Excel")
        print(f"  总耗时: {duration:.3f} 秒")
        print(f"  文件大小: {Path(file_path).stat().st_size / 1024 / 1024:.2f} MB")
        print(f"  导出速率: {total_points/duration:.0f} 点/秒")
        
        return duration
    
    def test_concurrent_processing(self, num_threads=10, operations_per_thread=1000):
        """测试并发处理性能"""
        print(f"测试并发处理性能 ({num_threads} 个线程, 每个 {operations_per_thread} 次操作)...")
        
        def worker_task(thread_id):
            """工作线程任务"""
            for i in range(operations_per_thread):
                module_id = f"concurrent_test_{thread_id}_{i}"
                
                raw_data = {
                    "module_id": module_id,
                    "device_id": f"concurrent_device_{thread_id}",
                    "slot_id": f"1/1/{i%4+1}",
                    "data_type": "perf_test_module",
                    "temperature": 25.0 + (i % 10),
                    "voltage": 3.3 + (i % 100) * 0.001
                }
                
                self.data_processor.process_module_data(module_id, raw_data)
            
            return thread_id
        
        # 并发测试
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [executor.submit(worker_task, i) for i in range(num_threads)]
            
            for future in as_completed(futures):
                thread_id = future.result()
        
        end_time = time.time()
        duration = end_time - start_time
        total_operations = num_threads * operations_per_thread
        
        print(f"  完成 {total_operations} 次并发操作")
        print(f"  总耗时: {duration:.3f} 秒")
        print(f"  并发处理速率: {total_operations/duration:.0f} 操作/秒")
        
        return duration
    
    def test_memory_usage(self, num_data_points=100000):
        """测试内存使用情况"""
        print(f"测试内存使用情况 ({num_data_points} 个数据点)...")
        
        try:
            import psutil
            process = psutil.Process()
            
            # 记录初始内存
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            print(f"  初始内存使用: {initial_memory:.2f} MB")
            
            # 处理大量数据
            module_id = "memory_test_module"
            
            for i in range(num_data_points):
                raw_data = {
                    "module_id": module_id,
                    "device_id": "memory_test_device",
                    "slot_id": "1/1/1",
                    "data_type": "perf_test_module",
                    "temperature": 25.0 + (i % 100) * 0.1,
                    "voltage": 3.3 + (i % 1000) * 0.0001,
                    "timestamp": f"2023-01-01 {i//3600:02d}:{(i%3600)//60:02d}:{i%60:02d}"
                }
                
                self.data_processor.process_module_data(module_id, raw_data)
                
                # 每10000个数据点检查一次内存
                if (i + 1) % 10000 == 0:
                    current_memory = process.memory_info().rss / 1024 / 1024
                    print(f"    处理 {i+1} 个数据点, 内存使用: {current_memory:.2f} MB")
            
            # 记录最终内存
            final_memory = process.memory_info().rss / 1024 / 1024
            memory_increase = final_memory - initial_memory
            
            print(f"  最终内存使用: {final_memory:.2f} MB")
            print(f"  内存增长: {memory_increase:.2f} MB")
            print(f"  平均每个数据点: {memory_increase*1024/num_data_points:.3f} KB")
            
        except ImportError:
            print("  psutil未安装，无法测试内存使用情况")
    
    def run_all_tests(self):
        """运行所有性能测试"""
        print("=" * 60)
        print("开始性能测试")
        print("=" * 60)
        
        try:
            # 数据验证性能测试
            self.test_data_validation_performance(10000)
            print()
            
            # 数据处理性能测试
            self.test_data_processing_performance(50, 500)
            print()
            
            # Excel导出性能测试
            self.test_excel_export_performance(5, 1000)
            print()
            
            # 并发处理性能测试
            self.test_concurrent_processing(5, 500)
            print()
            
            # 内存使用测试
            self.test_memory_usage(50000)
            print()
            
            print("=" * 60)
            print("性能测试完成")
            print("=" * 60)
            
        except Exception as e:
            print(f"性能测试失败: {str(e)}")
            raise
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理测试环境"""
        try:
            self.data_processor.cleanup()
            
            # 清理测试文件
            import shutil
            
            test_dirs = ["perf_test_config", "perf_test_output"]
            for test_dir in test_dirs:
                test_path = Path(test_dir)
                if test_path.exists():
                    shutil.rmtree(test_path)
            
            print("测试环境清理完成")
            
        except Exception as e:
            print(f"清理测试环境失败: {str(e)}")


def main():
    """主函数"""
    try:
        perf_test = PerformanceTest()
        perf_test.run_all_tests()
        return 0
    except Exception as e:
        print(f"性能测试异常: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
