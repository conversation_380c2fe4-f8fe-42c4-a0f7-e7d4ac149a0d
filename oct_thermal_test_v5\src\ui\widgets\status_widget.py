"""
状态显示控件
"""

from typing import Dict, Any
from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QProgressBar
from PySide6.QtCore import Qt, QTimer, Signal
from PySide6.QtGui import QColor, QPalette

from src.signals.app_signals import get_app_signals
from src.utils.logger import get_logger


class StatusIndicator(QWidget):
    """状态指示器"""
    
    def __init__(self, label: str = "状态"):
        super().__init__()
        self.logger = get_logger(__name__)
        
        self._status = "unknown"
        self._color_map = {
            "unknown": QColor(128, 128, 128),    # 灰色
            "disconnected": QColor(128, 128, 128),  # 灰色
            "connecting": QColor(255, 165, 0),   # 橙色
            "connected": QColor(0, 255, 0),      # 绿色
            "testing": QColor(0, 0, 255),        # 蓝色
            "passed": QColor(0, 255, 0),         # 绿色
            "failed": QColor(255, 0, 0),         # 红色
            "error": QColor(255, 0, 0)           # 红色
        }
        
        self._setup_ui(label)
    
    def _setup_ui(self, label: str):
        """设置UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 标签
        self.label = QLabel(label)
        layout.addWidget(self.label)
        
        # 状态指示器
        self.indicator = QLabel("●")
        self.indicator.setAlignment(Qt.AlignCenter)
        self.indicator.setFixedSize(20, 20)
        layout.addWidget(self.indicator)
        
        # 状态文本
        self.status_text = QLabel("未知")
        layout.addWidget(self.status_text)
        
        # 设置初始状态
        self.set_status("unknown")
    
    def set_status(self, status: str):
        """设置状态"""
        self._status = status
        
        # 更新颜色
        color = self._color_map.get(status, QColor(128, 128, 128))
        self.indicator.setStyleSheet(f"color: rgb({color.red()}, {color.green()}, {color.blue()});")
        
        # 更新文本
        status_text_map = {
            "unknown": "未知",
            "disconnected": "已断开",
            "connecting": "连接中",
            "connected": "已连接",
            "testing": "测试中",
            "passed": "通过",
            "failed": "失败",
            "error": "错误"
        }
        
        self.status_text.setText(status_text_map.get(status, status))
    
    def get_status(self) -> str:
        """获取状态"""
        return self._status


class DeviceStatusWidget(QWidget):
    """设备状态控件"""
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        
        # 设备状态指示器
        self._device_indicators: Dict[str, StatusIndicator] = {}
        
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self):
        """设置UI"""
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(5, 5, 5, 5)
        
        # 标题
        title = QLabel("设备状态")
        title.setStyleSheet("font-weight: bold; font-size: 12px;")
        self.layout.addWidget(title)
    
    def _connect_signals(self):
        """连接信号"""
        app_signals = get_app_signals()
        app_signals.device_connected.connect(self._on_device_connected)
        app_signals.device_disconnected.connect(self._on_device_disconnected)
        app_signals.device_status_changed.connect(self._on_device_status_changed)
    
    def add_device(self, device_id: str, device_name: str = ""):
        """添加设备"""
        if device_id in self._device_indicators:
            return
        
        display_name = device_name if device_name else device_id
        indicator = StatusIndicator(display_name)
        
        self._device_indicators[device_id] = indicator
        self.layout.addWidget(indicator)
        
        self.logger.info(f"添加设备状态指示器: {device_id}")
    
    def remove_device(self, device_id: str):
        """移除设备"""
        if device_id in self._device_indicators:
            indicator = self._device_indicators[device_id]
            self.layout.removeWidget(indicator)
            indicator.deleteLater()
            del self._device_indicators[device_id]
            
            self.logger.info(f"移除设备状态指示器: {device_id}")
    
    def _on_device_connected(self, device_id: str):
        """设备连接"""
        if device_id in self._device_indicators:
            self._device_indicators[device_id].set_status("connected")
    
    def _on_device_disconnected(self, device_id: str):
        """设备断开"""
        if device_id in self._device_indicators:
            self._device_indicators[device_id].set_status("disconnected")
    
    def _on_device_status_changed(self, device_id: str, status: str):
        """设备状态变更"""
        if device_id in self._device_indicators:
            self._device_indicators[device_id].set_status(status)


class TestProgressWidget(QWidget):
    """测试进度控件"""
    
    progress_updated = Signal(int)  # 进度更新信号
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        
        self._total_modules = 0
        self._completed_modules = 0
        self._failed_modules = 0
        
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 标题
        title = QLabel("测试进度")
        title.setStyleSheet("font-weight: bold; font-size: 12px;")
        layout.addWidget(title)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        layout.addWidget(self.progress_bar)
        
        # 统计信息
        stats_layout = QHBoxLayout()
        
        self.total_label = QLabel("总数: 0")
        stats_layout.addWidget(self.total_label)
        
        self.completed_label = QLabel("完成: 0")
        stats_layout.addWidget(self.completed_label)
        
        self.failed_label = QLabel("失败: 0")
        self.failed_label.setStyleSheet("color: red;")
        stats_layout.addWidget(self.failed_label)
        
        layout.addLayout(stats_layout)
    
    def _connect_signals(self):
        """连接信号"""
        app_signals = get_app_signals()
        app_signals.test_started.connect(self._on_test_started)
        app_signals.test_completed.connect(self._on_test_completed)
        app_signals.module_status_changed.connect(self._on_module_status_changed)
    
    def set_total_modules(self, total: int):
        """设置总模块数"""
        self._total_modules = total
        self._update_display()
    
    def _on_test_started(self, session_id: str):
        """测试开始"""
        self._completed_modules = 0
        self._failed_modules = 0
        self._update_display()
    
    def _on_test_completed(self, session_id: str, success: bool):
        """测试完成"""
        if success:
            self.progress_bar.setValue(100)
        else:
            self.progress_bar.setStyleSheet("QProgressBar::chunk { background-color: red; }")
    
    def _on_module_status_changed(self, module_id: str, status: str):
        """模块状态变更"""
        if status in ["passed", "failed"]:
            self._completed_modules += 1
            
            if status == "failed":
                self._failed_modules += 1
            
            self._update_display()
    
    def _update_display(self):
        """更新显示"""
        # 更新标签
        self.total_label.setText(f"总数: {self._total_modules}")
        self.completed_label.setText(f"完成: {self._completed_modules}")
        self.failed_label.setText(f"失败: {self._failed_modules}")
        
        # 更新进度条
        if self._total_modules > 0:
            progress = int((self._completed_modules / self._total_modules) * 100)
            self.progress_bar.setValue(progress)
            self.progress_updated.emit(progress)
        
        # 设置进度条颜色
        if self._failed_modules > 0:
            self.progress_bar.setStyleSheet("QProgressBar::chunk { background-color: orange; }")
        else:
            self.progress_bar.setStyleSheet("")


class SystemInfoWidget(QWidget):
    """系统信息控件"""
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        
        self._setup_ui()
        self._start_update_timer()
    
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # 标题
        title = QLabel("系统信息")
        title.setStyleSheet("font-weight: bold; font-size: 12px;")
        layout.addWidget(title)
        
        # 信息标签
        self.time_label = QLabel("时间: --")
        layout.addWidget(self.time_label)
        
        self.memory_label = QLabel("内存: --")
        layout.addWidget(self.memory_label)
        
        self.cpu_label = QLabel("CPU: --")
        layout.addWidget(self.cpu_label)
    
    def _start_update_timer(self):
        """启动更新定时器"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._update_info)
        self.update_timer.start(5000)  # 每5秒更新一次
    
    def _update_info(self):
        """更新信息"""
        try:
            from datetime import datetime
            import psutil
            
            # 更新时间
            current_time = datetime.now().strftime("%H:%M:%S")
            self.time_label.setText(f"时间: {current_time}")
            
            # 更新内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            self.memory_label.setText(f"内存: {memory_percent:.1f}%")
            
            # 更新CPU使用率
            cpu_percent = psutil.cpu_percent()
            self.cpu_label.setText(f"CPU: {cpu_percent:.1f}%")
            
        except ImportError:
            # 如果没有psutil，只显示时间
            from datetime import datetime
            current_time = datetime.now().strftime("%H:%M:%S")
            self.time_label.setText(f"时间: {current_time}")
            self.memory_label.setText("内存: N/A")
            self.cpu_label.setText("CPU: N/A")
        except Exception as e:
            self.logger.error(f"更新系统信息失败: {str(e)}")
