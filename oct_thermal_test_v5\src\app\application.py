"""
应用程序主类
"""

import sys
import signal
from typing import Optional
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer, QObject

from .config_manager import ConfigManager
from src.core.device_manager import DeviceManager
from src.core.test_controller import TestController
from src.core.data_processor import DataProcessor
from src.ui.main_window import MainWindow
from src.signals.app_signals import get_app_signals
from src.utils.logger import get_logger, set_log_level
from src.devices.base import DeviceManager as BaseDeviceManager


class Application(QObject):
    """应用程序主类"""
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        
        # 核心组件
        self.qt_app: Optional[QApplication] = None
        self.config_manager: Optional[ConfigManager] = None
        self.device_manager: Optional[DeviceManager] = None
        self.test_controller: Optional[TestController] = None
        self.data_processor: Optional[DataProcessor] = None
        self.main_window: Optional[MainWindow] = None
        
        # 信号连接
        self.app_signals = get_app_signals()
        
        # 初始化标志
        self._initialized = False
        self._running = False
    
    def initialize(self, config_dir: str = "config") -> bool:
        """初始化应用程序"""
        try:
            self.logger.info("开始初始化应用程序...")
            
            # 创建Qt应用程序
            if not QApplication.instance():
                self.qt_app = QApplication(sys.argv)
                self.qt_app.setApplicationName("OCT Thermal Test V5")
                self.qt_app.setApplicationVersion("5.0.0")
                self.qt_app.setOrganizationName("OCT Test Team")
            else:
                self.qt_app = QApplication.instance()
            
            # 初始化核心组件
            self._initialize_core_components(config_dir)
            
            # 创建主窗口
            self._create_main_window()
            
            # 连接信号
            self._connect_signals()
            
            # 设置信号处理
            self._setup_signal_handlers()
            
            self._initialized = True
            self.logger.info("应用程序初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"应用程序初始化失败: {str(e)}")
            return False
    
    def _initialize_core_components(self, config_dir: str):
        """初始化核心组件"""
        # 配置管理器
        self.config_manager = ConfigManager(config_dir)
        
        # 设置日志级别
        log_level = self.config_manager.get_app_config("log_level", "INFO")
        set_log_level(log_level)
        
        # 设备管理器
        self.device_manager = DeviceManager(self.config_manager)

        # 数据处理器
        self.data_processor = DataProcessor(self.config_manager)

        # 测试控制器
        self.test_controller = TestController(
            self.config_manager,
            self.device_manager
        )
        
        self.logger.info("核心组件初始化完成")
    
    def _create_main_window(self):
        """创建主窗口"""
        if not self.config_manager:
            raise RuntimeError("配置管理器未初始化")
        
        self.main_window = MainWindow(self.config_manager)
        
        # 连接应用程序退出信号
        if self.qt_app:
            self.qt_app.aboutToQuit.connect(self._on_about_to_quit)
        
        self.logger.info("主窗口创建完成")
    
    def _connect_signals(self):
        """连接信号"""
        # 应用程序信号
        self.app_signals.app_stopping.connect(self._on_app_stopping)
        
        # 设备管理器信号
        if self.device_manager:
            self.device_manager.device_added.connect(
                lambda device_id: self.app_signals.device_added.emit(device_id)
            )
            self.device_manager.device_removed.connect(
                lambda device_id: self.app_signals.device_removed.emit(device_id)
            )
            self.device_manager.device_connected.connect(
                lambda device_id: self.app_signals.device_connected.emit(device_id)
            )
            self.device_manager.device_disconnected.connect(
                lambda device_id: self.app_signals.device_disconnected.emit(device_id)
            )
        
        # 测试控制器信号
        if self.test_controller:
            self.test_controller.test_started.connect(
                lambda session_id: self.app_signals.test_started.emit(session_id)
            )
            self.test_controller.test_stopped.connect(
                lambda session_id: self.app_signals.test_stopped.emit(session_id)
            )
            self.test_controller.test_completed.connect(
                lambda session_id, success: self.app_signals.test_completed.emit(session_id, success)
            )

        # 数据处理器信号
        if self.data_processor:
            self.data_processor.data_processed.connect(
                lambda module_id, data: self.app_signals.module_data_updated.emit(module_id, data)
            )
            self.data_processor.validation_completed.connect(
                lambda module_id, results: self._on_validation_completed(module_id, results)
            )
            self.data_processor.excel_exported.connect(
                lambda file_path, module_id: self.app_signals.excel_saved.emit(file_path, module_id)
            )
    
    def _setup_signal_handlers(self):
        """设置系统信号处理"""
        # 处理Ctrl+C
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # 使用定时器来检查信号（Qt在Windows上的信号处理限制）
        self.signal_timer = QTimer()
        self.signal_timer.timeout.connect(lambda: None)
        self.signal_timer.start(100)
    
    def _signal_handler(self, signum, frame):
        """系统信号处理器"""
        self.logger.info(f"收到信号 {signum}，准备退出...")
        self.quit()
    
    def run(self) -> int:
        """运行应用程序"""
        if not self._initialized:
            self.logger.error("应用程序未初始化")
            return 1
        
        if not self.qt_app or not self.main_window:
            self.logger.error("Qt应用程序或主窗口未创建")
            return 1
        
        try:
            self.logger.info("启动应用程序...")
            self._running = True
            
            # 显示主窗口
            self.main_window.show()
            
            # 发送启动信号
            self.app_signals.app_started.emit()
            
            self.logger.info("应用程序启动完成")
            
            # 运行Qt事件循环
            return self.qt_app.exec()
            
        except Exception as e:
            self.logger.error(f"应用程序运行异常: {str(e)}")
            return 1
        finally:
            self._running = False
    
    def quit(self):
        """退出应用程序"""
        if not self._running:
            return
        
        self.logger.info("正在退出应用程序...")
        
        # 发送停止信号
        self.app_signals.app_stopping.emit()
        
        # 退出Qt应用程序
        if self.qt_app:
            self.qt_app.quit()
    
    def _on_app_stopping(self):
        """应用程序停止处理"""
        self.logger.info("应用程序正在停止...")
        
        # 停止测试
        if self.test_controller:
            self.test_controller.stop_all_tests()
        
        # 断开所有设备
        if self.device_manager:
            self.device_manager.disconnect_all_devices()
    
    def _on_validation_completed(self, module_id: str, results: dict):
        """验证完成处理"""
        from src.models.module_info import ValidationResult

        # 检查是否有失败的验证
        failed_fields = []
        for field, result_str in results.items():
            try:
                result = ValidationResult(result_str)
                if result == ValidationResult.FAIL:
                    failed_fields.append(field)
                    self.app_signals.validation_result.emit(module_id, field, False)
                else:
                    self.app_signals.validation_result.emit(module_id, field, True)
            except ValueError:
                continue

        # 更新模块状态
        if failed_fields:
            self.app_signals.module_status_changed.emit(module_id, "failed")
        else:
            self.app_signals.module_status_changed.emit(module_id, "passed")

    def _on_about_to_quit(self):
        """应用程序即将退出"""
        self.logger.info("应用程序即将退出")

        # 保存配置
        if self.config_manager:
            try:
                # 这里可以保存一些运行时配置
                pass
            except Exception as e:
                self.logger.error(f"保存配置失败: {str(e)}")

        # 清理资源
        self._cleanup()

        # 发送停止信号
        self.app_signals.app_stopped.emit()

        self.logger.info("应用程序清理完成")
    
    def _cleanup(self):
        """清理资源"""
        try:
            # 停止定时器
            if hasattr(self, 'signal_timer'):
                self.signal_timer.stop()
            
            # 清理设备管理器
            if self.device_manager:
                self.device_manager.cleanup()
            
            # 清理测试控制器
            if self.test_controller:
                self.test_controller.cleanup()

            # 清理数据处理器
            if self.data_processor:
                self.data_processor.cleanup()
                
        except Exception as e:
            self.logger.error(f"清理资源时发生错误: {str(e)}")
    
    # 属性访问器
    @property
    def is_initialized(self) -> bool:
        """是否已初始化"""
        return self._initialized
    
    @property
    def is_running(self) -> bool:
        """是否正在运行"""
        return self._running
    
    def get_config_manager(self) -> Optional[ConfigManager]:
        """获取配置管理器"""
        return self.config_manager
    
    def get_device_manager(self) -> Optional[DeviceManager]:
        """获取设备管理器"""
        return self.device_manager
    
    def get_test_controller(self) -> Optional[TestController]:
        """获取测试控制器"""
        return self.test_controller

    def get_data_processor(self) -> Optional[DataProcessor]:
        """获取数据处理器"""
        return self.data_processor

    def get_main_window(self) -> Optional[MainWindow]:
        """获取主窗口"""
        return self.main_window


# 全局应用程序实例
_app_instance: Optional[Application] = None


def get_application() -> Application:
    """获取应用程序实例"""
    global _app_instance
    if _app_instance is None:
        _app_instance = Application()
    return _app_instance


def create_application() -> Application:
    """创建新的应用程序实例"""
    global _app_instance
    _app_instance = Application()
    return _app_instance
