"""
设备Tab页面实现
"""

from typing import Dict, List, Optional, Any
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
    QTableWidgetItem, QHeaderView, QPushButton, QGroupBox,
    QLabel, QComboBox, QCheckBox, QSplitter, QTextEdit
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QColor

from src.app.config_manager import ConfigManager
from src.signals.app_signals import get_app_signals, get_ui_signals
from src.utils.logger import get_logger
from src.models.module_info import ModuleInfo, ModuleStatus


class DeviceTab(QWidget):
    """设备Tab页面"""
    
    def __init__(self, module_type: str, module_config: Optional[Dict[str, Any]], config_manager: ConfigManager):
        super().__init__()
        self.module_type = module_type
        self.module_config = module_config or {}
        self.config_manager = config_manager
        self.logger = get_logger(f"device_tab.{module_type}")
        
        # 信号连接
        self.app_signals = get_app_signals()
        self.ui_signals = get_ui_signals()
        
        # 数据管理
        self.modules: Dict[str, ModuleInfo] = {}  # 模块ID -> 模块信息
        self.table_rows: Dict[str, int] = {}      # 模块ID -> 表格行号
        
        # UI组件
        self.table_widget: Optional[QTableWidget] = None
        self.device_info_text: Optional[QTextEdit] = None
        self.status_label: Optional[QLabel] = None
        
        # 控制组件
        self.device_combo: Optional[QComboBox] = None
        self.auto_refresh_checkbox: Optional[QCheckBox] = None
        self.refresh_button: Optional[QPushButton] = None
        
        # 定时器
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self._auto_refresh)
        
        self._setup_ui()
        self._connect_signals()
        self._load_devices()
        
        self.logger.info(f"设备Tab初始化完成: {module_type}")
    
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 创建控制区域
        self._create_control_area(layout)
        
        # 创建主分割器
        main_splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(main_splitter)
        
        # 创建表格区域
        self._create_table_area(main_splitter)
        
        # 创建信息区域
        self._create_info_area(main_splitter)
        
        # 设置分割器比例
        main_splitter.setSizes([800, 300])
    
    def _create_control_area(self, parent_layout):
        """创建控制区域"""
        control_group = QGroupBox("设备控制")
        control_layout = QHBoxLayout(control_group)
        
        # 设备选择
        control_layout.addWidget(QLabel("设备:"))
        self.device_combo = QComboBox()
        self.device_combo.currentTextChanged.connect(self._on_device_changed)
        control_layout.addWidget(self.device_combo)
        
        control_layout.addStretch()
        
        # 自动刷新
        self.auto_refresh_checkbox = QCheckBox("自动刷新")
        self.auto_refresh_checkbox.toggled.connect(self._on_auto_refresh_toggled)
        control_layout.addWidget(self.auto_refresh_checkbox)
        
        # 手动刷新按钮
        self.refresh_button = QPushButton("刷新数据")
        self.refresh_button.clicked.connect(self._refresh_data)
        control_layout.addWidget(self.refresh_button)
        
        # 连接设备按钮
        connect_button = QPushButton("连接设备")
        connect_button.clicked.connect(self._connect_device)
        control_layout.addWidget(connect_button)
        
        # 断开设备按钮
        disconnect_button = QPushButton("断开设备")
        disconnect_button.clicked.connect(self._disconnect_device)
        control_layout.addWidget(disconnect_button)
        
        parent_layout.addWidget(control_group)
    
    def _create_table_area(self, parent):
        """创建表格区域"""
        table_group = QGroupBox(f"{self.module_type} 模块数据")
        table_layout = QVBoxLayout(table_group)
        
        # 创建表格
        self.table_widget = QTableWidget()
        self._setup_table()
        table_layout.addWidget(self.table_widget)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        table_layout.addWidget(self.status_label)
        
        parent.addWidget(table_group)
    
    def _create_info_area(self, parent):
        """创建信息区域"""
        info_group = QGroupBox("设备信息")
        info_layout = QVBoxLayout(info_group)
        
        # 设备信息文本
        self.device_info_text = QTextEdit()
        self.device_info_text.setReadOnly(True)
        self.device_info_text.setMaximumHeight(200)
        info_layout.addWidget(self.device_info_text)
        
        parent.addWidget(info_group)
    
    def _setup_table(self):
        """设置表格"""
        # 获取数据字段
        data_fields = self.module_config.get("data_fields", [
            "device_id", "slot_id", "serial_number", "status"
        ])
        
        # 添加固定列
        columns = ["设备ID", "槽位", "序列号", "状态"] + data_fields
        
        self.table_widget.setColumnCount(len(columns))
        self.table_widget.setHorizontalHeaderLabels(columns)
        
        # 设置表格属性
        self.table_widget.setAlternatingRowColors(True)
        self.table_widget.setSelectionBehavior(QTableWidget.SelectRows)
        self.table_widget.setSortingEnabled(True)
        
        # 设置列宽
        header = self.table_widget.horizontalHeader()
        header.setStretchLastSection(True)
        
        # 设置固定列宽
        self.table_widget.setColumnWidth(0, 100)  # 设备ID
        self.table_widget.setColumnWidth(1, 80)   # 槽位
        self.table_widget.setColumnWidth(2, 120)  # 序列号
        self.table_widget.setColumnWidth(3, 80)   # 状态
    
    def _connect_signals(self):
        """连接信号"""
        # 应用程序信号
        self.app_signals.module_data_updated.connect(self._on_module_data_updated)
        self.app_signals.module_status_changed.connect(self._on_module_status_changed)
        self.app_signals.device_connected.connect(self._on_device_connected)
        self.app_signals.device_disconnected.connect(self._on_device_disconnected)
        
        # UI信号
        self.ui_signals.table_row_added.connect(self._on_table_row_added)
        self.ui_signals.table_row_updated.connect(self._on_table_row_updated)
        self.ui_signals.table_row_highlight.connect(self._on_table_row_highlight)
    
    def _load_devices(self):
        """加载设备列表"""
        # 获取支持当前模块类型的设备
        device_names = self.config_manager.get_all_device_names()
        
        self.device_combo.clear()
        self.device_combo.addItem("选择设备...")
        
        for device_name in device_names:
            device_config = self.config_manager.get_device_config(device_name)
            if device_config and device_config.get("device_type") == "oct":
                # 检查是否有支持当前模块类型的槽位
                slots = device_config.get("slots", [])
                for slot in slots:
                    if slot.get("module_type") == self.module_type:
                        self.device_combo.addItem(device_name)
                        break
    
    def add_module(self, module_info: ModuleInfo):
        """添加模块"""
        module_id = module_info.unique_id
        
        if module_id in self.modules:
            self.logger.warning(f"模块已存在: {module_id}")
            return
        
        self.modules[module_id] = module_info
        
        # 添加到表格
        row = self.table_widget.rowCount()
        self.table_widget.insertRow(row)
        self.table_rows[module_id] = row
        
        # 设置基本信息
        self.table_widget.setItem(row, 0, QTableWidgetItem(module_info.device_id))
        self.table_widget.setItem(row, 1, QTableWidgetItem(module_info.slot_id))
        self.table_widget.setItem(row, 2, QTableWidgetItem(module_info.serial_number))
        self.table_widget.setItem(row, 3, QTableWidgetItem(module_info.status.value))
        
        # 设置状态颜色
        self._update_row_color(row, module_info.status)
        
        self.logger.info(f"添加测试模块: {module_info.slot_id}")
    
    def update_module_data(self, module_id: str, data: Dict[str, Any]):
        """更新模块数据"""
        if module_id not in self.modules:
            self.logger.warning(f"模块不存在: {module_id}")
            return
        
        if module_id not in self.table_rows:
            self.logger.warning(f"模块表格行不存在: {module_id}")
            return
        
        row = self.table_rows[module_id]
        
        # 更新数据字段
        data_fields = self.module_config.get("data_fields", [])
        for i, field in enumerate(data_fields):
            col = 4 + i  # 前4列是固定列
            if col < self.table_widget.columnCount():
                value = data.get(field, "")
                self.table_widget.setItem(row, col, QTableWidgetItem(str(value)))
        
        # 更新序列号
        if "serial_number" in data:
            self.modules[module_id].serial_number = str(data["serial_number"])
            self.table_widget.setItem(row, 2, QTableWidgetItem(str(data["serial_number"])))
    
    def update_module_status(self, module_id: str, status: ModuleStatus):
        """更新模块状态"""
        if module_id not in self.modules:
            return
        
        self.modules[module_id].update_status(status)
        
        if module_id in self.table_rows:
            row = self.table_rows[module_id]
            self.table_widget.setItem(row, 3, QTableWidgetItem(status.value))
            self._update_row_color(row, status)
    
    def _update_row_color(self, row: int, status: ModuleStatus):
        """更新行颜色"""
        if status == ModuleStatus.FAILED:
            color = QColor(255, 200, 200)  # 浅红色
        elif status == ModuleStatus.PASSED:
            color = QColor(200, 255, 200)  # 浅绿色
        elif status == ModuleStatus.TESTING:
            color = QColor(255, 255, 200)  # 浅黄色
        elif status == ModuleStatus.ERROR:
            color = QColor(255, 150, 150)  # 红色
        else:
            color = QColor(255, 255, 255)  # 白色
        
        for col in range(self.table_widget.columnCount()):
            item = self.table_widget.item(row, col)
            if item:
                item.setBackground(color)
    
    def clear_modules(self):
        """清空模块"""
        self.modules.clear()
        self.table_rows.clear()
        self.table_widget.setRowCount(0)
    
    def get_selected_modules(self) -> List[str]:
        """获取选中的模块ID"""
        selected_rows = set()
        for item in self.table_widget.selectedItems():
            selected_rows.add(item.row())
        
        selected_modules = []
        for module_id, row in self.table_rows.items():
            if row in selected_rows:
                selected_modules.append(module_id)
        
        return selected_modules
    
    # 事件处理方法
    def _on_device_changed(self, device_name: str):
        """设备变更"""
        if device_name == "选择设备...":
            return
        
        self.logger.info(f"选择设备: {device_name}")
        self._update_device_info(device_name)
    
    def _on_auto_refresh_toggled(self, checked: bool):
        """自动刷新切换"""
        if checked:
            interval = self.config_manager.get_app_config("single_loop_time", 10) * 1000
            self.refresh_timer.start(interval)
            self.logger.info("启用自动刷新")
        else:
            self.refresh_timer.stop()
            self.logger.info("禁用自动刷新")
    
    def _refresh_data(self):
        """刷新数据"""
        current_device = self.device_combo.currentText()
        if current_device and current_device != "选择设备...":
            self.logger.info(f"刷新设备数据: {current_device}")
            # TODO: 触发数据刷新
    
    def _auto_refresh(self):
        """自动刷新"""
        self._refresh_data()
    
    def _connect_device(self):
        """连接设备"""
        current_device = self.device_combo.currentText()
        if current_device and current_device != "选择设备...":
            self.app_signals.device_connected.emit(current_device)
    
    def _disconnect_device(self):
        """断开设备"""
        current_device = self.device_combo.currentText()
        if current_device and current_device != "选择设备...":
            self.app_signals.device_disconnected.emit(current_device)
    
    def _update_device_info(self, device_name: str):
        """更新设备信息"""
        device_config = self.config_manager.get_device_config(device_name)
        if device_config:
            info_text = f"设备名称: {device_config.get('device_name', device_name)}\n"
            info_text += f"设备类型: {device_config.get('device_type', 'unknown')}\n"
            
            connection = device_config.get('connection', {})
            info_text += f"连接类型: {connection.get('connection_type', 'unknown')}\n"
            info_text += f"主机地址: {connection.get('host', 'unknown')}\n"
            info_text += f"端口: {connection.get('port', 'unknown')}\n"
            
            slots = device_config.get('slots', [])
            module_type_slots = [s for s in slots if s.get('module_type') == self.module_type]
            info_text += f"支持槽位: {len(module_type_slots)} 个\n"
            
            self.device_info_text.setText(info_text)
    
    # 信号处理方法
    def _on_module_data_updated(self, module_id: str, data: Dict[str, Any]):
        """模块数据更新"""
        if module_id in self.modules:
            self.update_module_data(module_id, data)
    
    def _on_module_status_changed(self, module_id: str, status: str):
        """模块状态变更"""
        if module_id in self.modules:
            try:
                module_status = ModuleStatus(status)
                self.update_module_status(module_id, module_status)
            except ValueError:
                self.logger.warning(f"无效的模块状态: {status}")
    
    def _on_device_connected(self, device_id: str):
        """设备连接"""
        if device_id == self.device_combo.currentText():
            self.status_label.setText(f"设备已连接: {device_id}")
    
    def _on_device_disconnected(self, device_id: str):
        """设备断开"""
        if device_id == self.device_combo.currentText():
            self.status_label.setText(f"设备已断开: {device_id}")
    
    def _on_table_row_added(self, tab_name: str, row_data: Dict[str, Any]):
        """表格行添加"""
        if tab_name == self.module_type:
            # TODO: 处理表格行添加
            pass
    
    def _on_table_row_updated(self, tab_name: str, module_id: str, data: Dict[str, Any]):
        """表格行更新"""
        if tab_name == self.module_type:
            self.update_module_data(module_id, data)
    
    def _on_table_row_highlight(self, tab_name: str, module_id: str, color: str):
        """表格行高亮"""
        if tab_name == self.module_type and module_id in self.table_rows:
            row = self.table_rows[module_id]
            highlight_color = QColor(color)
            
            for col in range(self.table_widget.columnCount()):
                item = self.table_widget.item(row, col)
                if item:
                    item.setBackground(highlight_color)
