#!/usr/bin/env python3
"""
OCT Thermal Test V5 - 程序入口
基于PySide6的OCT设备热循环测试上位机软件
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

from src.app.application import create_application
from src.utils.logger import get_logger


def main():
    """主函数"""
    try:
        # 创建应用程序实例
        app = create_application()
        
        # 初始化应用程序
        if not app.initialize():
            print("应用程序初始化失败")
            return 1
        
        # 运行应用程序
        return app.run()
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        return 0
    except Exception as e:
        print(f"程序运行异常: {str(e)}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
