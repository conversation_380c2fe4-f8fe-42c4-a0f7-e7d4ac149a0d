"""
配置管理器
支持多设备、多模块类型的配置管理，支持Excel配置文件读取和多sheet管理
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
import pandas as pd
from PySide6.QtCore import QObject, Signal

from src.utils.logger import get_logger


class ConfigManager(QObject):
    """配置管理器"""
    
    # 配置变更信号
    config_changed = Signal(str, object)  # 配置键, 新值
    
    def __init__(self, config_dir: str = "config"):
        super().__init__()
        self.logger = get_logger(__name__)
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
        # 配置缓存
        self._app_config: Dict[str, Any] = {}
        self._device_configs: Dict[str, Dict[str, Any]] = {}
        self._module_type_configs: Dict[str, Dict[str, Any]] = {}
        
        # 加载配置
        self._load_all_configs()
    
    def _load_all_configs(self):
        """加载所有配置"""
        try:
            self._load_app_config()
            self._load_device_configs()
            self._load_module_type_configs()
            self.logger.info("所有配置加载完成")
        except Exception as e:
            self.logger.error(f"加载配置失败: {e}")
            raise
    
    def _load_app_config(self):
        """加载应用程序配置"""
        config_file = self.config_dir / "app_config.json"
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                self._app_config = json.load(f)
        else:
            # 创建默认配置
            self._app_config = self._get_default_app_config()
            self._save_app_config()
        
        self.logger.info(f"应用程序配置加载完成: {len(self._app_config)} 项")
    
    def _load_device_configs(self):
        """加载设备配置"""
        device_config_dir = self.config_dir / "device_configs"
        device_config_dir.mkdir(exist_ok=True)
        
        # 加载所有设备配置文件
        for config_file in device_config_dir.glob("*.json"):
            device_name = config_file.stem
            with open(config_file, 'r', encoding='utf-8') as f:
                self._device_configs[device_name] = json.load(f)
        
        self.logger.info(f"设备配置加载完成: {len(self._device_configs)} 个设备")
    
    def _load_module_type_configs(self):
        """加载模块类型配置"""
        module_type_dir = self.config_dir / "module_types"
        module_type_dir.mkdir(exist_ok=True)
        
        # 加载所有模块类型配置文件
        for config_file in module_type_dir.glob("*.json"):
            module_type = config_file.stem
            with open(config_file, 'r', encoding='utf-8') as f:
                self._module_type_configs[module_type] = json.load(f)
        
        self.logger.info(f"模块类型配置加载完成: {len(self._module_type_configs)} 种类型")
    
    def _get_default_app_config(self) -> Dict[str, Any]:
        """获取默认应用程序配置"""
        return {
            "app_name": "OCT Thermal Test V5",
            "version": "5.0.0",
            "log_level": "INFO",
            "log_dir": "logs",
            "output_dir": "output",
            "single_loop_time": 10,
            "max_retry_count": 3,
            "connection_timeout": 30,
            "data_validation_timeout": 300,
            "ui_settings": {
                "window_size": [1200, 800],
                "window_position": [100, 100],
                "theme": "default"
            },
            "excel_settings": {
                "auto_save": True,
                "save_interval": 60,
                "max_rows_per_sheet": 10000
            }
        }
    
    def _save_app_config(self):
        """保存应用程序配置"""
        config_file = self.config_dir / "app_config.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(self._app_config, f, indent=2, ensure_ascii=False)
    
    def get_app_config(self, key: str, default: Any = None) -> Any:
        """获取应用程序配置"""
        keys = key.split('.')
        value = self._app_config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value
    
    def set_app_config(self, key: str, value: Any):
        """设置应用程序配置"""
        keys = key.split('.')
        config = self._app_config
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        old_value = config.get(keys[-1])
        config[keys[-1]] = value
        
        # 保存配置
        self._save_app_config()
        
        # 发送配置变更信号
        if old_value != value:
            self.config_changed.emit(key, value)
        
        self.logger.debug(f"配置已更新: {key} = {value}")
    
    def get_device_config(self, device_name: str) -> Optional[Dict[str, Any]]:
        """获取设备配置"""
        return self._device_configs.get(device_name)
    
    def set_device_config(self, device_name: str, config: Dict[str, Any]):
        """设置设备配置"""
        self._device_configs[device_name] = config
        
        # 保存到文件
        config_file = self.config_dir / "device_configs" / f"{device_name}.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        self.config_changed.emit(f"device.{device_name}", config)
        self.logger.info(f"设备配置已更新: {device_name}")
    
    def get_module_type_config(self, module_type: str) -> Optional[Dict[str, Any]]:
        """获取模块类型配置"""
        return self._module_type_configs.get(module_type)
    
    def set_module_type_config(self, module_type: str, config: Dict[str, Any]):
        """设置模块类型配置"""
        self._module_type_configs[module_type] = config
        
        # 保存到文件
        config_file = self.config_dir / "module_types" / f"{module_type}.json"
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        self.config_changed.emit(f"module_type.{module_type}", config)
        self.logger.info(f"模块类型配置已更新: {module_type}")
    
    def get_all_device_names(self) -> List[str]:
        """获取所有设备名称"""
        return list(self._device_configs.keys())
    
    def get_all_module_types(self) -> List[str]:
        """获取所有模块类型"""
        return list(self._module_type_configs.keys())
    
    def load_excel_config(self, excel_path: str) -> Dict[str, pd.DataFrame]:
        """从Excel文件加载配置"""
        try:
            # 读取所有sheet
            excel_data = pd.read_excel(excel_path, sheet_name=None)
            
            # 处理每个sheet的数据
            processed_data = {}
            for sheet_name, df in excel_data.items():
                # 填充空值
                df = df.fillna("")
                processed_data[sheet_name] = df
                
                self.logger.info(f"加载Excel配置表: {sheet_name}, 行数: {len(df)}")
            
            return processed_data
            
        except Exception as e:
            self.logger.error(f"加载Excel配置失败: {excel_path}, 错误: {e}")
            raise
    
    def validate_device_config(self, config: Dict[str, Any]) -> bool:
        """验证设备配置"""
        required_fields = ["device_type", "connection_type", "ip_or_port", "slots"]
        
        for field in required_fields:
            if field not in config:
                self.logger.error(f"设备配置缺少必需字段: {field}")
                return False
        
        # 验证槽位配置
        slots = config.get("slots", [])
        if not isinstance(slots, list) or len(slots) == 0:
            self.logger.error("设备配置中槽位信息无效")
            return False
        
        for slot in slots:
            if not isinstance(slot, dict) or "slot_id" not in slot:
                self.logger.error(f"槽位配置无效: {slot}")
                return False
        
        return True
    
    def validate_module_type_config(self, config: Dict[str, Any]) -> bool:
        """验证模块类型配置"""
        required_fields = ["module_type", "data_fields", "validation_rules"]
        
        for field in required_fields:
            if field not in config:
                self.logger.error(f"模块类型配置缺少必需字段: {field}")
                return False
        
        return True
