"""
设备配置数据模型
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from enum import Enum


class ConnectionType(Enum):
    """连接类型枚举"""
    SSH = "ssh"
    SERIAL = "serial"


class DeviceType(Enum):
    """设备类型枚举"""
    OCT = "oct"
    NPB = "npb"


@dataclass
class SlotConfig:
    """槽位配置"""
    slot_id: str  # 槽位ID，如 "1/1/1"
    slot_name: str = ""  # 槽位名称
    max_modules: int = 2  # 最大模块数
    enabled: bool = True  # 是否启用
    npb_ports: List[str] = field(default_factory=list)  # 绑定的NPB端口
    module_type: str = ""  # 模块类型
    
    def __post_init__(self):
        if not self.slot_name:
            self.slot_name = f"槽位 {self.slot_id}"


@dataclass
class ConnectionConfig:
    """连接配置"""
    connection_type: ConnectionType
    host: str  # IP地址或串口号
    port: int = 22  # 端口号或波特率
    username: str = "admin"
    password: str = "Admin_123"
    timeout: int = 30
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "connection_type": self.connection_type.value,
            "host": self.host,
            "port": self.port,
            "username": self.username,
            "password": self.password,
            "timeout": self.timeout
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConnectionConfig':
        """从字典创建"""
        return cls(
            connection_type=ConnectionType(data["connection_type"]),
            host=data["host"],
            port=data.get("port", 22),
            username=data.get("username", "admin"),
            password=data.get("password", "Admin_123"),
            timeout=data.get("timeout", 30)
        )


@dataclass
class DeviceConfig:
    """设备配置"""
    device_id: str  # 设备ID
    device_name: str  # 设备名称
    device_type: DeviceType  # 设备类型
    connection: ConnectionConfig  # 连接配置
    slots: List[SlotConfig] = field(default_factory=list)  # 槽位配置
    enabled: bool = True  # 是否启用
    description: str = ""  # 描述
    
    def __post_init__(self):
        if not self.device_name:
            self.device_name = f"{self.device_type.value.upper()} {self.device_id}"
    
    def get_slot_by_id(self, slot_id: str) -> Optional[SlotConfig]:
        """根据槽位ID获取槽位配置"""
        for slot in self.slots:
            if slot.slot_id == slot_id:
                return slot
        return None
    
    def get_enabled_slots(self) -> List[SlotConfig]:
        """获取启用的槽位"""
        return [slot for slot in self.slots if slot.enabled]
    
    def get_slots_by_module_type(self, module_type: str) -> List[SlotConfig]:
        """根据模块类型获取槽位"""
        return [slot for slot in self.slots if slot.module_type == module_type]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "device_id": self.device_id,
            "device_name": self.device_name,
            "device_type": self.device_type.value,
            "connection": self.connection.to_dict(),
            "slots": [
                {
                    "slot_id": slot.slot_id,
                    "slot_name": slot.slot_name,
                    "max_modules": slot.max_modules,
                    "enabled": slot.enabled,
                    "npb_ports": slot.npb_ports,
                    "module_type": slot.module_type
                }
                for slot in self.slots
            ],
            "enabled": self.enabled,
            "description": self.description
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DeviceConfig':
        """从字典创建"""
        slots = []
        for slot_data in data.get("slots", []):
            slot = SlotConfig(
                slot_id=slot_data["slot_id"],
                slot_name=slot_data.get("slot_name", ""),
                max_modules=slot_data.get("max_modules", 2),
                enabled=slot_data.get("enabled", True),
                npb_ports=slot_data.get("npb_ports", []),
                module_type=slot_data.get("module_type", "")
            )
            slots.append(slot)
        
        return cls(
            device_id=data["device_id"],
            device_name=data.get("device_name", ""),
            device_type=DeviceType(data["device_type"]),
            connection=ConnectionConfig.from_dict(data["connection"]),
            slots=slots,
            enabled=data.get("enabled", True),
            description=data.get("description", "")
        )


@dataclass
class NPBPortConfig:
    """NPB端口配置"""
    npb_ip: str  # NPB设备IP
    port_name: str  # 端口名称，如 "CE2"
    enabled: bool = True
    description: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "npb_ip": self.npb_ip,
            "port_name": self.port_name,
            "enabled": self.enabled,
            "description": self.description
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'NPBPortConfig':
        """从字典创建"""
        return cls(
            npb_ip=data["npb_ip"],
            port_name=data["port_name"],
            enabled=data.get("enabled", True),
            description=data.get("description", "")
        )


@dataclass
class DeviceMapping:
    """设备映射配置"""
    oct_device_id: str  # OCT设备ID
    oct_slot_id: str  # OCT槽位ID
    npb_ports: List[NPBPortConfig]  # 绑定的NPB端口
    module_type: str = ""  # 模块类型
    enabled: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "oct_device_id": self.oct_device_id,
            "oct_slot_id": self.oct_slot_id,
            "npb_ports": [port.to_dict() for port in self.npb_ports],
            "module_type": self.module_type,
            "enabled": self.enabled
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DeviceMapping':
        """从字典创建"""
        npb_ports = [
            NPBPortConfig.from_dict(port_data)
            for port_data in data.get("npb_ports", [])
        ]
        
        return cls(
            oct_device_id=data["oct_device_id"],
            oct_slot_id=data["oct_slot_id"],
            npb_ports=npb_ports,
            module_type=data.get("module_type", ""),
            enabled=data.get("enabled", True)
        )
