"""
设备管理器
"""

from typing import Dict, List, Optional, Any
from PySide6.QtCore import QObject, Signal

from src.app.config_manager import ConfigManager
from src.devices.base import DeviceManager as BaseDeviceManager
from src.devices.oct_device import OCTDevice
from src.devices.npb_device import NPBDevice
from src.devices.ssh_connection import SSHConnection
from src.devices.serial_connection import SerialConnection
from src.models.device_config import DeviceConfig, ConnectionType, DeviceType
from src.utils.logger import get_logger


class DeviceManager(QObject):
    """设备管理器"""
    
    # 设备管理信号
    device_added = Signal(str)           # 设备ID
    device_removed = Signal(str)         # 设备ID
    device_connected = Signal(str)       # 设备ID
    device_disconnected = Signal(str)    # 设备ID
    device_status_changed = Signal(str, str)  # 设备ID, 状态
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        self.config_manager = config_manager
        self.logger = get_logger(__name__)
        
        # 基础设备管理器
        self.base_manager = BaseDeviceManager()
        
        # 设备配置缓存
        self._device_configs: Dict[str, DeviceConfig] = {}
        
        # OCT和NPB设备分类管理
        self._oct_devices: Dict[str, OCTDevice] = {}
        self._npb_devices: Dict[str, NPBDevice] = {}
        
        self._connect_signals()
        self._load_device_configs()
        
        self.logger.info("设备管理器初始化完成")
    
    def _connect_signals(self):
        """连接信号"""
        # 基础设备管理器信号
        self.base_manager.device_added.connect(self.device_added.emit)
        self.base_manager.device_removed.connect(self.device_removed.emit)
        self.base_manager.device_connected.connect(self.device_connected.emit)
        self.base_manager.device_disconnected.connect(self.device_disconnected.emit)
    
    def _load_device_configs(self):
        """加载设备配置"""
        device_names = self.config_manager.get_all_device_names()
        
        for device_name in device_names:
            config_dict = self.config_manager.get_device_config(device_name)
            if config_dict:
                try:
                    device_config = DeviceConfig.from_dict(config_dict)
                    self._device_configs[device_name] = device_config
                    self._create_device(device_config)
                except Exception as e:
                    self.logger.error(f"加载设备配置失败: {device_name}, 错误: {str(e)}")
        
        self.logger.info(f"加载设备配置完成，共 {len(self._device_configs)} 个设备")
    
    def _create_device(self, device_config: DeviceConfig):
        """创建设备实例"""
        try:
            if device_config.device_type == DeviceType.OCT:
                self._create_oct_device(device_config)
            elif device_config.device_type == DeviceType.NPB:
                self._create_npb_device(device_config)
            else:
                self.logger.error(f"不支持的设备类型: {device_config.device_type}")
                
        except Exception as e:
            self.logger.error(f"创建设备失败: {device_config.device_id}, 错误: {str(e)}")
    
    def _create_oct_device(self, device_config: DeviceConfig):
        """创建OCT设备"""
        # 创建连接
        connection = self._create_connection(device_config)
        if not connection:
            return
        
        # 创建OCT设备
        oct_device = OCTDevice(device_config, connection)
        
        # 添加到管理器
        self.base_manager.add_device(oct_device)
        self._oct_devices[device_config.device_id] = oct_device
        
        self.logger.info(f"创建OCT设备: {device_config.device_id}")
    
    def _create_npb_device(self, device_config: DeviceConfig):
        """创建NPB设备"""
        connection_config = device_config.connection
        
        # 创建NPB设备（使用内置HTTP连接）
        npb_device = NPBDevice(
            device_config.device_id,
            connection_config.host,
            connection_config.username,
            connection_config.password
        )
        
        # 添加到管理器
        self.base_manager.add_device(npb_device)
        self._npb_devices[device_config.device_id] = npb_device
        
        self.logger.info(f"创建NPB设备: {device_config.device_id}")
    
    def _create_connection(self, device_config: DeviceConfig):
        """创建设备连接"""
        connection_config = device_config.connection
        
        try:
            if connection_config.connection_type == ConnectionType.SSH:
                return SSHConnection(
                    connection_id=f"{device_config.device_id}_ssh",
                    host=connection_config.host,
                    port=connection_config.port,
                    username=connection_config.username,
                    password=connection_config.password,
                    timeout=connection_config.timeout
                )
            elif connection_config.connection_type == ConnectionType.SERIAL:
                return SerialConnection(
                    connection_id=f"{device_config.device_id}_serial",
                    port=connection_config.host,  # 串口号
                    baudrate=connection_config.port,  # 波特率
                    timeout=connection_config.timeout
                )
            else:
                self.logger.error(f"不支持的连接类型: {connection_config.connection_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"创建连接失败: {device_config.device_id}, 错误: {str(e)}")
            return None
    
    def add_device_config(self, device_config: DeviceConfig):
        """添加设备配置"""
        device_id = device_config.device_id
        
        # 保存配置
        self.config_manager.set_device_config(device_id, device_config.to_dict())
        self._device_configs[device_id] = device_config
        
        # 创建设备
        self._create_device(device_config)
        
        self.logger.info(f"添加设备配置: {device_id}")
    
    def remove_device_config(self, device_id: str):
        """移除设备配置"""
        if device_id in self._device_configs:
            # 移除设备实例
            self.base_manager.remove_device(device_id)
            
            # 移除缓存
            del self._device_configs[device_id]
            if device_id in self._oct_devices:
                del self._oct_devices[device_id]
            if device_id in self._npb_devices:
                del self._npb_devices[device_id]
            
            self.logger.info(f"移除设备配置: {device_id}")
    
    def get_device_config(self, device_id: str) -> Optional[DeviceConfig]:
        """获取设备配置"""
        return self._device_configs.get(device_id)
    
    def get_oct_device(self, device_id: str) -> Optional[OCTDevice]:
        """获取OCT设备"""
        return self._oct_devices.get(device_id)
    
    def get_npb_device(self, device_id: str) -> Optional[NPBDevice]:
        """获取NPB设备"""
        return self._npb_devices.get(device_id)
    
    def get_all_oct_devices(self) -> List[OCTDevice]:
        """获取所有OCT设备"""
        return list(self._oct_devices.values())
    
    def get_all_npb_devices(self) -> List[NPBDevice]:
        """获取所有NPB设备"""
        return list(self._npb_devices.values())
    
    def get_enabled_oct_devices(self) -> List[OCTDevice]:
        """获取启用的OCT设备"""
        enabled_devices = []
        for device_id, device in self._oct_devices.items():
            config = self._device_configs.get(device_id)
            if config and config.enabled:
                enabled_devices.append(device)
        return enabled_devices
    
    def get_enabled_npb_devices(self) -> List[NPBDevice]:
        """获取启用的NPB设备"""
        enabled_devices = []
        for device_id, device in self._npb_devices.items():
            config = self._device_configs.get(device_id)
            if config and config.enabled:
                enabled_devices.append(device)
        return enabled_devices
    
    def connect_device(self, device_id: str) -> bool:
        """连接设备"""
        return self.base_manager.connect_device(device_id)
    
    def disconnect_device(self, device_id: str):
        """断开设备连接"""
        self.base_manager.disconnect_device(device_id)
    
    def connect_all_devices(self) -> Dict[str, bool]:
        """连接所有设备"""
        return self.base_manager.connect_all_devices()
    
    def disconnect_all_devices(self):
        """断开所有设备连接"""
        self.base_manager.disconnect_all_devices()
    
    def get_connected_devices(self) -> List[str]:
        """获取已连接的设备ID列表"""
        connected_devices = self.base_manager.get_connected_devices()
        return [device.device_id for device in connected_devices]
    
    def get_device_status(self, device_id: str) -> Optional[Dict[str, Any]]:
        """获取设备状态"""
        device = self.base_manager.get_device(device_id)
        if device:
            return device.get_status()
        return None
    
    def get_all_devices_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有设备状态"""
        return self.base_manager.get_devices_status()
    
    def initialize_device(self, device_id: str) -> bool:
        """初始化设备"""
        device = self.base_manager.get_device(device_id)
        if device:
            return device.initialize()
        return False
    
    def initialize_all_devices(self) -> Dict[str, bool]:
        """初始化所有设备"""
        results = {}
        for device_id in self._device_configs.keys():
            results[device_id] = self.initialize_device(device_id)
        return results
    
    def check_device_health(self, device_id: str) -> bool:
        """检查设备健康状态"""
        device = self.base_manager.get_device(device_id)
        if device:
            return device.check_health()
        return False
    
    def check_all_devices_health(self) -> Dict[str, bool]:
        """检查所有设备健康状态"""
        results = {}
        for device_id in self._device_configs.keys():
            results[device_id] = self.check_device_health(device_id)
        return results
    
    def get_devices_by_module_type(self, module_type: str) -> List[OCTDevice]:
        """根据模块类型获取OCT设备"""
        devices = []
        for device_id, device in self._oct_devices.items():
            config = self._device_configs.get(device_id)
            if config:
                # 检查是否有支持该模块类型的槽位
                for slot in config.slots:
                    if slot.module_type == module_type and slot.enabled:
                        devices.append(device)
                        break
        return devices
    
    def cleanup(self):
        """清理资源"""
        try:
            self.disconnect_all_devices()
            self._oct_devices.clear()
            self._npb_devices.clear()
            self._device_configs.clear()
            self.logger.info("设备管理器清理完成")
        except Exception as e:
            self.logger.error(f"设备管理器清理失败: {str(e)}")
    
    def reload_configs(self):
        """重新加载配置"""
        self.logger.info("重新加载设备配置...")
        self.cleanup()
        self._load_device_configs()
