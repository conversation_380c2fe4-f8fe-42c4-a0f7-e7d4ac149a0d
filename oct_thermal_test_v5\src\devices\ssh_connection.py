"""
SSH连接实现 - 基于原始代码逻辑
"""

import time
import socket
from typing import Optional
import paramiko

from .base import DeviceConnection, ConnectionStatus


class SSHConnection(DeviceConnection):
    """SSH连接实现 - 参考原始ssh.py"""

    def __init__(
        self,
        connection_id: str,
        host: str,
        port: int = 22,
        username: str = "admin",
        password: str = "Admin_123",
        timeout: int = 30
    ):
        super().__init__(connection_id)
        self.ip = host  # 保持与原始代码一致的属性名
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.timeout = timeout

        self._client: Optional[paramiko.SSHClient] = None
        self._shell: Optional[paramiko.Channel] = None
        self.connection_status = False  # 保持与原始代码一致
    
    def connect(self) -> bool:
        """建立SSH连接 - 参考原始ssh.py的connect方法"""
        try:
            self._set_status(ConnectionStatus.CONNECTING)

            # 如果已有连接，先关闭
            if self.connection_status:
                self.close()

            # 创建SSH客户端
            self._client = paramiko.SSHClient()
            self._client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            # 建立连接
            self._client.connect(
                hostname=self.host,
                port=self.port,
                username=self.username,
                password=self.password,
                timeout=self.timeout
            )

            # 创建交互式shell
            self._shell = self._client.invoke_shell()
            time.sleep(1)  # 等待shell初始化

            # 清除欢迎信息
            if self._shell.recv_ready():
                self._shell.recv(65535)

            self.connection_status = True
            self._set_status(ConnectionStatus.CONNECTED)
            self.logger.info(f"SSH连接成功: {self.host}:{self.port}")
            return True

        except paramiko.AuthenticationException as e:
            error_msg = f"SSH认证失败: {self.host}:{self.port}, 用户名: {self.username}"
            self.connection_status = False
            self._set_status(ConnectionStatus.ERROR, error_msg)
            return False

        except paramiko.SSHException as e:
            error_msg = f"SSH连接失败: {self.host}:{self.port}, 错误: {str(e)}"
            self.connection_status = False
            self._set_status(ConnectionStatus.ERROR, error_msg)
            return False

        except socket.timeout:
            error_msg = f"SSH连接超时: {self.host}:{self.port}"
            self.connection_status = False
            self._set_status(ConnectionStatus.ERROR, error_msg)
            return False

        except Exception as e:
            error_msg = f"SSH连接异常: {self.host}:{self.port}, 错误: {str(e)}"
            self.connection_status = False
            self._set_status(ConnectionStatus.ERROR, error_msg)
            return False
    
    def close(self):
        """关闭SSH连接 - 参考原始代码"""
        try:
            if self._shell:
                self._shell.close()
                self._shell = None

            if self._client:
                self._client.close()
                self._client = None

            self.connection_status = False
            self.logger.info(f"SSH连接已关闭: {self.host}:{self.port}")

        except Exception as e:
            self.logger.error(f"关闭SSH连接时发生错误: {str(e)}")

    def disconnect(self):
        """断开SSH连接"""
        self.close()
        self._set_status(ConnectionStatus.DISCONNECTED)
    
    def send_command(self, command: str, timeout: Optional[int] = None) -> str:
        """发送命令并获取响应 - 参考原始代码逻辑"""
        if not self.connection_status or not self._shell:
            raise ConnectionError("SSH连接未建立")

        try:
            # 使用默认超时时间
            if timeout is None:
                timeout = self.timeout

            # 发送命令
            self._shell.send(f'{command}\n')
            time.sleep(0.5)  # 等待命令执行

            # 读取响应
            output = self._recv_all_data(timeout)

            # 处理特殊情况（如需要重新登录）
            if 'Login:' in output:
                self._handle_relogin()
                self._shell.send(f'{command}\n')
                output = self._recv_all_data(timeout)

            # 处理配置模式提示符
            if '<' in output and '>' in output:
                self._shell.send('sys\n')
                time.sleep(0.5)
                self._shell.send(f'{command}\n')
                output = self._recv_all_data(timeout)

            self.data_received.emit(output)
            return output

        except Exception as e:
            error_msg = f"发送SSH命令失败: {command}, 错误: {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)
    
    def _recv_all_data(self, timeout: int = 10) -> str:
        """接收所有可用数据"""
        if not self._shell:
            return ""
        
        output = ""
        start_time = time.time()
        
        while True:
            if self._shell.recv_ready():
                data = self._shell.recv(4096)
                try:
                    output += data.decode('utf-8', errors='ignore')
                except UnicodeDecodeError:
                    output += data.decode('gbk', errors='ignore')
            else:
                time.sleep(0.1)
            
            # 检查超时
            if time.time() - start_time > timeout:
                break
            
            # 如果没有更多数据且已经有输出，则退出
            if not self._shell.recv_ready() and output:
                time.sleep(0.2)  # 再等待一下确保没有更多数据
                if not self._shell.recv_ready():
                    break
        
        return output
    
    def _handle_relogin(self):
        """处理重新登录"""
        try:
            self._shell.send(f'{self.username}\n')
            time.sleep(0.5)
            self._shell.send(f'{self.password}\n')
            time.sleep(0.5)
            self._shell.send('sys\n')
            time.sleep(0.5)
            
            # 清除登录响应
            if self._shell.recv_ready():
                self._shell.recv(65535)
                
        except Exception as e:
            self.logger.error(f"重新登录失败: {str(e)}")
    
    def is_alive(self) -> bool:
        """检查连接是否存活"""
        return self.connection_status and self._client is not None and self._shell is not None

    @property
    def is_connected(self) -> bool:
        """是否已连接 - 保持与基类接口一致"""
        return self.connection_status
    
    def execute_command(self, command: str, timeout: Optional[int] = None) -> tuple[str, str]:
        """执行单次命令（非交互式）"""
        if not self.is_connected or not self._client:
            raise ConnectionError("SSH连接未建立")
        
        try:
            if timeout is None:
                timeout = self.timeout
            
            stdin, stdout, stderr = self._client.exec_command(command, timeout=timeout)
            
            output = stdout.read().decode('utf-8', errors='ignore')
            error = stderr.read().decode('utf-8', errors='ignore')
            
            return output, error
            
        except Exception as e:
            error_msg = f"执行SSH命令失败: {command}, 错误: {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)
    
    def get_connection_info(self) -> dict:
        """获取连接信息"""
        info = super().get_connection_info()
        info.update({
            "connection_type": "ssh",
            "host": self.host,
            "port": self.port,
            "username": self.username,
            "timeout": self.timeout
        })
        return info
