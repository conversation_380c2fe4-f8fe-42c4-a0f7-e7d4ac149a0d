2025-07-12 10:14:08 | INFO | src.app.application:initialize:47 | 开始初始化应用程序...
2025-07-12 10:14:08 | INFO | src.app.config_manager:_load_app_config:58 | 应用程序配置加载完成: 14 项
2025-07-12 10:14:08 | INFO | src.app.config_manager:_load_device_configs:71 | 设备配置加载完成: 1 个设备
2025-07-12 10:14:08 | INFO | src.app.config_manager:_load_module_type_configs:84 | 模块类型配置加载完成: 1 种类型
2025-07-12 10:14:08 | INFO | src.app.config_manager:_load_all_configs:42 | 所有配置加载完成
2025-07-12 10:14:08 | ERROR | src.core.device_manager:_create_device:83 | 创建设备失败: oct_001, 错误: SSHConnection.connect() takes 1 positional argument but 4 were given
2025-07-12 10:14:08 | INFO | src.core.device_manager:_load_device_configs:70 | 加载设备配置完成，共 1 个设备
2025-07-12 10:14:08 | INFO | src.core.device_manager:__init__:46 | 设备管理器初始化完成
2025-07-12 10:14:08 | INFO | src.core.data_processor:_start_auto_save:67 | 自动保存已启用，间隔: 60秒
2025-07-12 10:14:08 | INFO | src.core.data_processor:__init__:55 | 数据处理器初始化完成
2025-07-12 10:14:08 | INFO | src.core.test_controller:__init__:356 | 测试控制器初始化完成
2025-07-12 10:14:08 | INFO | src.app.application:_initialize_core_components:99 | 核心组件初始化完成
2025-07-12 10:14:08 | ERROR | src.app.application:initialize:75 | 应用程序初始化失败: 'UISignals' object has no attribute 'table_row_added'
