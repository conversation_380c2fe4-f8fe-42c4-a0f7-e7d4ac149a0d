#!/usr/bin/env python3
"""
简单测试 - 不依赖复杂的导入
"""

def test_basic():
    """基础测试"""
    print("基础测试开始...")
    
    # 测试基本的Python功能
    try:
        import sys
        import os
        from pathlib import Path
        print("✓ 基本模块导入成功")
        
        # 测试PySide6
        from PySide6.QtCore import QObject, Signal
        print("✓ PySide6导入成功")
        
        # 测试其他依赖
        import pandas as pd
        print("✓ pandas导入成功")
        
        import paramiko
        print("✓ paramiko导入成功")
        
        import requests
        print("✓ requests导入成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 基础测试失败: {e}")
        return False

def test_ssh_basic():
    """测试SSH基础功能"""
    print("\n测试SSH基础功能...")
    
    try:
        import paramiko
        
        # 创建SSH客户端
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        print("✓ SSH客户端创建成功")
        client.close()
        
        return True
        
    except Exception as e:
        print(f"✗ SSH测试失败: {e}")
        return False

def test_config_basic():
    """测试配置基础功能"""
    print("\n测试配置基础功能...")
    
    try:
        import json
        from pathlib import Path
        
        # 创建测试配置
        test_config = {
            "app_name": "OCT Thermal Test V5",
            "version": "5.0.0",
            "test_setting": "test_value"
        }
        
        # 写入配置文件
        config_file = Path("test_config.json")
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(test_config, f, indent=2, ensure_ascii=False)
        
        # 读取配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            loaded_config = json.load(f)
        
        # 验证配置
        if loaded_config["test_setting"] == "test_value":
            print("✓ 配置读写功能正常")
        else:
            print("✗ 配置读写功能异常")
            return False
        
        # 清理测试文件
        config_file.unlink()
        
        return True
        
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False

def test_data_processing():
    """测试数据处理功能"""
    print("\n测试数据处理功能...")
    
    try:
        import pandas as pd
        import re
        
        # 模拟OCT设备数据
        test_data = """
        Vendor SN                     :	OCT2N825030165  
        Module Temperature            :	55.00 C
        Module Power Supply           :	3.37 V
        Current Output Power          :	0.00 dBm
        Current Input Power           :	-0.40 dBm
        """
        
        # 解析数据
        data_dict = {}
        lines = test_data.strip().split('\n')
        
        for line in lines:
            if ':' in line:
                parts = line.split(':', 1)
                if len(parts) == 2:
                    key = parts[0].strip()
                    value = parts[1].strip()
                    data_dict[key] = value
        
        # 验证解析结果
        if "Vendor SN" in data_dict and "Module Temperature" in data_dict:
            print("✓ 数据解析功能正常")
            print(f"  解析到 {len(data_dict)} 个字段")
        else:
            print("✗ 数据解析功能异常")
            return False
        
        # 测试pandas处理
        df = pd.DataFrame([data_dict])
        print(f"✓ pandas数据处理正常，数据形状: {df.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ 数据处理测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("OCT Thermal Test V5 - 简单功能测试")
    print("=" * 50)
    
    tests = [
        test_basic,
        test_ssh_basic,
        test_config_basic,
        test_data_processing,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有简单功能测试通过！")
        print("\n基础环境和依赖包工作正常，可以继续开发。")
        return 0
    else:
        print("✗ 部分测试失败，请检查错误信息")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
