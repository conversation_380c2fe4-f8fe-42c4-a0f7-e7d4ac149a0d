#!/usr/bin/env python3
"""
测试运行脚本
"""

import sys
import os
import argparse
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))


def run_integration_tests():
    """运行集成测试"""
    print("运行集成测试...")
    
    try:
        from tests.test_integration import run_integration_tests
        return run_integration_tests()
    except ImportError as e:
        print(f"导入集成测试模块失败: {e}")
        return 1
    except Exception as e:
        print(f"运行集成测试失败: {e}")
        return 1


def run_performance_tests():
    """运行性能测试"""
    print("运行性能测试...")
    
    try:
        from tests.test_performance import main as perf_main
        return perf_main()
    except ImportError as e:
        print(f"导入性能测试模块失败: {e}")
        return 1
    except Exception as e:
        print(f"运行性能测试失败: {e}")
        return 1


def check_dependencies():
    """检查依赖包"""
    print("检查依赖包...")
    
    required_packages = [
        "PySide6",
        "pandas",
        "openpyxl",
        "paramiko",
        "pyserial",
        "requests"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✓ {package}")
        except ImportError:
            print(f"  ✗ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺失的依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("所有依赖包已安装")
    return True


def create_sample_config():
    """创建示例配置"""
    print("创建示例配置...")
    
    try:
        from src.app.config_manager import ConfigManager
        from src.models.device_config import DeviceConfig, ConnectionConfig, SlotConfig, ConnectionType, DeviceType
        
        config_manager = ConfigManager()
        
        # 创建示例OCT设备配置
        oct_config = DeviceConfig(
            device_id="oct_001",
            device_name="OCT设备001",
            device_type=DeviceType.OCT,
            connection=ConnectionConfig(
                connection_type=ConnectionType.SSH,
                host="**************",
                port=22,
                username="admin",
                password="Admin_123",
                timeout=30
            ),
            slots=[
                SlotConfig(
                    slot_id="1/1/1",
                    slot_name="槽位 1/1/1",
                    max_modules=2,
                    enabled=True,
                    npb_ports=["CE2", "CE3", "CE4", "CE5"],
                    module_type="cfp2_dco"
                ),
                SlotConfig(
                    slot_id="1/1/2",
                    slot_name="槽位 1/1/2",
                    max_modules=2,
                    enabled=True,
                    npb_ports=["CE6", "CE7", "CE8", "CE9"],
                    module_type="cfp2_dco"
                )
            ],
            enabled=True,
            description="示例OCT设备"
        )
        
        config_manager.set_device_config("oct_001", oct_config.to_dict())
        
        # 创建示例NPB设备配置
        npb_config = DeviceConfig(
            device_id="npb_001",
            device_name="NPB设备001",
            device_type=DeviceType.NPB,
            connection=ConnectionConfig(
                connection_type=ConnectionType.SSH,  # NPB使用HTTP，这里只是示例
                host="**************",
                port=443,
                username="admin",
                password="Admin_123",
                timeout=30
            ),
            enabled=True,
            description="示例NPB设备"
        )
        
        config_manager.set_device_config("npb_001", npb_config.to_dict())
        
        print("示例配置创建完成")
        return True
        
    except Exception as e:
        print(f"创建示例配置失败: {e}")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="OCT Thermal Test V5 测试运行器")
    parser.add_argument(
        "test_type",
        choices=["integration", "performance", "all", "deps", "config"],
        help="测试类型: integration(集成测试), performance(性能测试), all(全部测试), deps(检查依赖), config(创建示例配置)"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="详细输出"
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        print(f"Python版本: {sys.version}")
        print(f"工作目录: {os.getcwd()}")
        print(f"脚本目录: {current_dir}")
        print()
    
    if args.test_type == "deps":
        return 0 if check_dependencies() else 1
    
    elif args.test_type == "config":
        return 0 if create_sample_config() else 1
    
    elif args.test_type == "integration":
        # 先检查依赖
        if not check_dependencies():
            return 1
        
        return run_integration_tests()
    
    elif args.test_type == "performance":
        # 先检查依赖
        if not check_dependencies():
            return 1
        
        return run_performance_tests()
    
    elif args.test_type == "all":
        # 先检查依赖
        if not check_dependencies():
            return 1
        
        print("=" * 60)
        print("运行所有测试")
        print("=" * 60)
        
        # 运行集成测试
        integration_result = run_integration_tests()
        print()
        
        # 运行性能测试
        performance_result = run_performance_tests()
        
        print("=" * 60)
        print("测试结果汇总:")
        print(f"  集成测试: {'通过' if integration_result == 0 else '失败'}")
        print(f"  性能测试: {'通过' if performance_result == 0 else '失败'}")
        print("=" * 60)
        
        return max(integration_result, performance_result)
    
    else:
        parser.print_help()
        return 1


if __name__ == "__main__":
    sys.exit(main())
