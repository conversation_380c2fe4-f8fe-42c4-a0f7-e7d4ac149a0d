"""
测试流程控制器
"""

import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from PySide6.QtCore import QObject, Signal, QThread, QTimer

from src.app.config_manager import ConfigManager
from src.core.device_manager import DeviceManager
from src.models.test_data import TestSession, TestDataPoint, TestStatus
from src.models.module_info import ModuleInfo, ModuleStatus, ValidationResult
from src.utils.logger import get_logger
from src.signals.app_signals import get_test_signals


class TestWorkerThread(QThread):
    """测试工作线程"""
    
    # 信号定义
    step_started = Signal(str, str)           # 会话ID, 步骤名称
    step_completed = Signal(str, str, bool)   # 会话ID, 步骤名称, 是否成功
    step_failed = Signal(str, str, str)       # 会话ID, 步骤名称, 错误信息
    data_collected = Signal(str, str, dict)   # 会话ID, 模块ID, 数据
    test_completed = Signal(str, bool)        # 会话ID, 是否成功
    
    def __init__(self, session: TestSession, config_manager: Config<PERSON>anager, device_manager: <PERSON>ceManager):
        super().__init__()
        self.session = session
        self.config_manager = config_manager
        self.device_manager = device_manager
        self.logger = get_logger(f"test_worker.{session.session_id}")
        
        self._stop_requested = False
        self._paused = False
    
    def run(self):
        """运行测试"""
        try:
            self.logger.info(f"开始测试会话: {self.session.session_id}")
            self.session.status = TestStatus.RUNNING
            
            # 执行测试步骤
            success = self._execute_test_steps()
            
            # 完成测试
            self.session.finish_test()
            self.test_completed.emit(self.session.session_id, success)
            
        except Exception as e:
            self.logger.error(f"测试执行异常: {str(e)}")
            self.session.status = TestStatus.ERROR
            self.test_completed.emit(self.session.session_id, False)
    
    def _execute_test_steps(self) -> bool:
        """执行测试步骤"""
        try:
            # 步骤1: 连接设备
            if not self._connect_devices():
                return False
            
            # 步骤2: 检查NPB端口稳定性
            if not self._check_npb_stability():
                return False
            
            # 步骤3: 清零NPB错误数
            if not self._clear_npb_errors():
                return False
            
            # 步骤4: 初始化OCT设备
            if not self._initialize_oct_devices():
                return False
            
            # 步骤5: 检查模块存在性
            if not self._check_modules_existence():
                return False
            
            # 步骤6: 执行主测试循环
            if not self._execute_main_test_loop():
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"测试步骤执行失败: {str(e)}")
            return False
    
    def _connect_devices(self) -> bool:
        """连接设备"""
        self.step_started.emit(self.session.session_id, "连接设备")
        
        try:
            # 连接所有设备
            results = self.device_manager.connect_all_devices()
            
            failed_devices = [device_id for device_id, success in results.items() if not success]
            
            if failed_devices:
                error_msg = f"设备连接失败: {', '.join(failed_devices)}"
                self.step_failed.emit(self.session.session_id, "连接设备", error_msg)
                return False
            
            self.step_completed.emit(self.session.session_id, "连接设备", True)
            return True
            
        except Exception as e:
            self.step_failed.emit(self.session.session_id, "连接设备", str(e))
            return False
    
    def _check_npb_stability(self) -> bool:
        """检查NPB端口稳定性"""
        self.step_started.emit(self.session.session_id, "检查NPB稳定性")
        
        try:
            stability_timeout = self.config_manager.get_app_config("test_settings.stability_check_timeout", 300)
            check_interval = self.config_manager.get_app_config("test_settings.stability_check_interval", 10)
            
            start_time = datetime.now()
            
            while (datetime.now() - start_time).total_seconds() < stability_timeout:
                if self._stop_requested:
                    return False
                
                all_stable = True
                unstable_info = []
                
                # 检查所有NPB设备的端口稳定性
                npb_devices = self.device_manager.get_enabled_npb_devices()
                
                for npb_device in npb_devices:
                    # 获取需要检查的端口列表
                    ports_to_check = self._get_npb_ports_for_device(npb_device.device_id)
                    
                    # 检查端口稳定性
                    stability_results = npb_device.check_ports_stability(ports_to_check)
                    
                    for port, is_stable in stability_results.items():
                        if not is_stable:
                            all_stable = False
                            traffic_data = npb_device.get_port_traffic(port)
                            unstable_info.append({
                                "npb_device": npb_device.device_id,
                                "port": port,
                                "tx_rate": traffic_data.get("tx_rate", 0),
                                "rx_rate": traffic_data.get("rx_rate", 0)
                            })
                
                if all_stable:
                    self.step_completed.emit(self.session.session_id, "检查NPB稳定性", True)
                    return True
                
                # 等待下次检查
                time.sleep(check_interval)
            
            # 超时仍不稳定
            error_msg = f"NPB端口稳定性检查超时，不稳定端口: {unstable_info}"
            self.step_failed.emit(self.session.session_id, "检查NPB稳定性", error_msg)
            return False
            
        except Exception as e:
            self.step_failed.emit(self.session.session_id, "检查NPB稳定性", str(e))
            return False
    
    def _clear_npb_errors(self) -> bool:
        """清零NPB错误数"""
        self.step_started.emit(self.session.session_id, "清零NPB错误数")
        
        try:
            npb_devices = self.device_manager.get_enabled_npb_devices()
            
            for npb_device in npb_devices:
                ports_to_clear = self._get_npb_ports_for_device(npb_device.device_id)
                results = npb_device.clear_all_errors(ports_to_clear)
                
                failed_ports = [port for port, success in results.items() if not success]
                if failed_ports:
                    error_msg = f"清零失败的端口: {npb_device.device_id} - {failed_ports}"
                    self.step_failed.emit(self.session.session_id, "清零NPB错误数", error_msg)
                    return False
            
            self.step_completed.emit(self.session.session_id, "清零NPB错误数", True)
            return True
            
        except Exception as e:
            self.step_failed.emit(self.session.session_id, "清零NPB错误数", str(e))
            return False
    
    def _initialize_oct_devices(self) -> bool:
        """初始化OCT设备"""
        self.step_started.emit(self.session.session_id, "初始化OCT设备")
        
        try:
            results = self.device_manager.initialize_all_devices()
            
            failed_devices = [device_id for device_id, success in results.items() if not success]
            
            if failed_devices:
                error_msg = f"OCT设备初始化失败: {', '.join(failed_devices)}"
                self.step_failed.emit(self.session.session_id, "初始化OCT设备", error_msg)
                return False
            
            self.step_completed.emit(self.session.session_id, "初始化OCT设备", True)
            return True
            
        except Exception as e:
            self.step_failed.emit(self.session.session_id, "初始化OCT设备", str(e))
            return False
    
    def _check_modules_existence(self) -> bool:
        """检查模块存在性"""
        self.step_started.emit(self.session.session_id, "检查模块存在性")
        
        try:
            oct_devices = self.device_manager.get_enabled_oct_devices()
            
            for oct_device in oct_devices:
                existence_results = oct_device.check_module_exists()
                
                for slot_id, exists in existence_results.items():
                    module_id = f"{oct_device.device_id}_{slot_id}"
                    
                    if exists:
                        # 创建模块信息
                        module_info = ModuleInfo(
                            device_id=oct_device.device_id,
                            slot_id=slot_id,
                            module_type="",  # 稍后获取
                            status=ModuleStatus.CONNECTED
                        )
                        
                        # 添加到会话
                        if module_id not in self.session.module_ids:
                            self.session.module_ids.append(module_id)
                    else:
                        self.logger.warning(f"槽位 {slot_id} 未检测到模块")
            
            if not self.session.module_ids:
                error_msg = "未检测到任何模块"
                self.step_failed.emit(self.session.session_id, "检查模块存在性", error_msg)
                return False
            
            self.step_completed.emit(self.session.session_id, "检查模块存在性", True)
            return True
            
        except Exception as e:
            self.step_failed.emit(self.session.session_id, "检查模块存在性", str(e))
            return False
    
    def _execute_main_test_loop(self) -> bool:
        """执行主测试循环"""
        self.step_started.emit(self.session.session_id, "执行主测试循环")
        
        try:
            loop_interval = self.config_manager.get_app_config("single_loop_time", 10)
            
            while not self._stop_requested:
                loop_start_time = datetime.now()
                
                # 处理暂停
                while self._paused and not self._stop_requested:
                    time.sleep(0.1)
                
                if self._stop_requested:
                    break
                
                # 收集所有模块数据
                self._collect_all_modules_data()
                
                # 计算睡眠时间
                loop_end_time = datetime.now()
                loop_duration = (loop_end_time - loop_start_time).total_seconds()
                sleep_time = max(0, loop_interval - loop_duration)
                
                if sleep_time > 0:
                    time.sleep(sleep_time)
            
            self.step_completed.emit(self.session.session_id, "执行主测试循环", True)
            return True
            
        except Exception as e:
            self.step_failed.emit(self.session.session_id, "执行主测试循环", str(e))
            return False
    
    def _collect_all_modules_data(self):
        """收集所有模块数据"""
        oct_devices = self.device_manager.get_enabled_oct_devices()
        
        for oct_device in oct_devices:
            try:
                # 读取所有模块数据
                all_data = oct_device.read_all_modules_data()
                
                for slot_id, module_data in all_data.items():
                    module_id = f"{oct_device.device_id}_{slot_id}"
                    
                    if module_id in self.session.module_ids:
                        # 创建测试数据点
                        data_point = TestDataPoint(
                            timestamp=datetime.now(),
                            module_id=module_id,
                            data=module_data
                        )
                        
                        # 添加到会话
                        self.session.add_data_point(data_point)
                        
                        # 发送数据收集信号
                        self.data_collected.emit(self.session.session_id, module_id, module_data)
                        
            except Exception as e:
                self.logger.error(f"收集模块数据失败: {oct_device.device_id}, 错误: {str(e)}")
    
    def _get_npb_ports_for_device(self, npb_device_id: str) -> List[str]:
        """获取NPB设备需要检查的端口列表"""
        # TODO: 根据配置获取端口列表
        return ["CE2", "CE3", "CE4", "CE5"]  # 示例端口
    
    def stop(self):
        """停止测试"""
        self._stop_requested = True
    
    def pause(self):
        """暂停测试"""
        self._paused = True
    
    def resume(self):
        """恢复测试"""
        self._paused = False


class TestController(QObject):
    """测试流程控制器"""
    
    # 测试控制信号
    test_started = Signal(str)                # 会话ID
    test_stopped = Signal(str)                # 会话ID
    test_paused = Signal(str)                 # 会话ID
    test_resumed = Signal(str)                # 会话ID
    test_completed = Signal(str, bool)        # 会话ID, 是否成功
    
    def __init__(self, config_manager: ConfigManager, device_manager: DeviceManager):
        super().__init__()
        self.config_manager = config_manager
        self.device_manager = device_manager
        self.logger = get_logger(__name__)
        
        # 测试会话管理
        self._active_sessions: Dict[str, TestSession] = {}
        self._worker_threads: Dict[str, TestWorkerThread] = {}
        
        # 信号连接
        self.test_signals = get_test_signals()
        self._connect_signals()
        
        self.logger.info("测试控制器初始化完成")
    
    def _connect_signals(self):
        """连接信号"""
        # 连接测试信号
        self.test_signals.session_started.connect(self.test_started.emit)
        self.test_signals.session_stopped.connect(self.test_stopped.emit)
        self.test_signals.session_completed.connect(self._on_session_completed)
    
    def start_test(self, session_id: str, config: Optional[Dict[str, Any]] = None) -> bool:
        """开始测试"""
        try:
            if session_id in self._active_sessions:
                self.logger.warning(f"测试会话已存在: {session_id}")
                return False
            
            # 创建测试会话
            session = TestSession(
                session_id=session_id,
                start_time=datetime.now(),
                config=config or {}
            )
            
            # 创建工作线程
            worker = TestWorkerThread(session, self.config_manager, self.device_manager)
            
            # 连接工作线程信号
            worker.test_completed.connect(self._on_test_completed)
            worker.step_started.connect(self.test_signals.step_started.emit)
            worker.step_completed.connect(self.test_signals.step_completed.emit)
            worker.step_failed.connect(self.test_signals.step_failed.emit)
            worker.data_collected.connect(self._on_data_collected)
            
            # 保存会话和线程
            self._active_sessions[session_id] = session
            self._worker_threads[session_id] = worker
            
            # 启动测试
            worker.start()
            
            self.test_started.emit(session_id)
            self.logger.info(f"测试已启动: {session_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"启动测试失败: {session_id}, 错误: {str(e)}")
            return False
    
    def stop_test(self, session_id: str):
        """停止测试"""
        if session_id in self._worker_threads:
            worker = self._worker_threads[session_id]
            worker.stop()
            worker.wait(5000)  # 等待5秒
            
            self.test_stopped.emit(session_id)
            self.logger.info(f"测试已停止: {session_id}")
    
    def pause_test(self, session_id: str):
        """暂停测试"""
        if session_id in self._worker_threads:
            worker = self._worker_threads[session_id]
            worker.pause()
            
            self.test_paused.emit(session_id)
            self.logger.info(f"测试已暂停: {session_id}")
    
    def resume_test(self, session_id: str):
        """恢复测试"""
        if session_id in self._worker_threads:
            worker = self._worker_threads[session_id]
            worker.resume()
            
            self.test_resumed.emit(session_id)
            self.logger.info(f"测试已恢复: {session_id}")
    
    def stop_all_tests(self):
        """停止所有测试"""
        for session_id in list(self._active_sessions.keys()):
            self.stop_test(session_id)
    
    def get_active_sessions(self) -> List[str]:
        """获取活动会话列表"""
        return list(self._active_sessions.keys())
    
    def get_session(self, session_id: str) -> Optional[TestSession]:
        """获取测试会话"""
        return self._active_sessions.get(session_id)
    
    def _on_test_completed(self, session_id: str, success: bool):
        """测试完成处理"""
        self.test_completed.emit(session_id, success)
        
        # 清理资源
        if session_id in self._worker_threads:
            del self._worker_threads[session_id]
        
        if session_id in self._active_sessions:
            session = self._active_sessions[session_id]
            self.test_signals.session_completed.emit(session_id, session.results)
            del self._active_sessions[session_id]
        
        self.logger.info(f"测试完成: {session_id}, 成功: {success}")
    
    def _on_session_completed(self, session_id: str, results: Dict[str, Any]):
        """会话完成处理"""
        self.logger.info(f"会话完成: {session_id}, 结果: {results}")
    
    def _on_data_collected(self, session_id: str, module_id: str, data: Dict[str, Any]):
        """数据收集处理"""
        # 发送数据更新信号
        from src.signals.app_signals import get_app_signals
        app_signals = get_app_signals()
        app_signals.module_data_updated.emit(module_id, data)
    
    def cleanup(self):
        """清理资源"""
        try:
            self.stop_all_tests()
            self._active_sessions.clear()
            self._worker_threads.clear()
            self.logger.info("测试控制器清理完成")
        except Exception as e:
            self.logger.error(f"测试控制器清理失败: {str(e)}")
