"""
Excel处理工具
"""

import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import pandas as pd
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows

from src.utils.logger import get_logger


class ExcelHandler:
    """Excel处理器"""
    
    def __init__(self, output_dir: str = "output"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.logger = get_logger(__name__)
        
        # 样式定义
        self._define_styles()
    
    def _define_styles(self):
        """定义Excel样式"""
        # 字体样式
        self.header_font = Font(name='Arial', size=12, bold=True, color='FFFFFF')
        self.data_font = Font(name='Arial', size=10)
        self.error_font = Font(name='Arial', size=10, color='FF0000')
        
        # 填充样式
        self.header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
        self.pass_fill = PatternFill(start_color='C6EFCE', end_color='C6EFCE', fill_type='solid')
        self.fail_fill = PatternFill(start_color='FFC7CE', end_color='FFC7CE', fill_type='solid')
        self.warning_fill = PatternFill(start_color='FFEB9C', end_color='FFEB9C', fill_type='solid')
        
        # 对齐样式
        self.center_alignment = Alignment(horizontal='center', vertical='center')
        self.left_alignment = Alignment(horizontal='left', vertical='center')
        
        # 边框样式
        thin_border = Side(border_style='thin', color='000000')
        self.border = Border(left=thin_border, right=thin_border, top=thin_border, bottom=thin_border)
    
    def create_test_report(
        self,
        session_id: str,
        module_data: Dict[str, List[Dict[str, Any]]],
        error_data: Dict[str, List[Dict[str, Any]]],
        summary: Dict[str, Any],
        filename: Optional[str] = None
    ) -> str:
        """创建测试报告"""
        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"test_report_{session_id}_{timestamp}.xlsx"
            
            file_path = self.output_dir / filename
            
            # 创建工作簿
            wb = Workbook()
            
            # 删除默认工作表
            wb.remove(wb.active)
            
            # 创建汇总表
            self._create_summary_sheet(wb, summary)
            
            # 为每个模块创建数据表
            for module_id, data_list in module_data.items():
                if data_list:
                    self._create_module_data_sheet(wb, module_id, data_list)
            
            # 为每个模块创建错误表
            for module_id, error_list in error_data.items():
                if error_list:
                    self._create_module_error_sheet(wb, module_id, error_list)
            
            # 保存文件
            wb.save(file_path)
            
            self.logger.info(f"测试报告已保存: {file_path}")
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"创建测试报告失败: {str(e)}")
            raise
    
    def _create_summary_sheet(self, workbook: Workbook, summary: Dict[str, Any]):
        """创建汇总表"""
        ws = workbook.create_sheet("测试汇总", 0)
        
        # 设置列宽
        ws.column_dimensions['A'].width = 20
        ws.column_dimensions['B'].width = 30
        
        # 添加标题
        ws['A1'] = "测试汇总报告"
        ws['A1'].font = Font(name='Arial', size=16, bold=True)
        ws.merge_cells('A1:B1')
        
        # 添加汇总信息
        row = 3
        summary_items = [
            ("测试会话ID", summary.get("session_id", "")),
            ("开始时间", summary.get("start_time", "")),
            ("结束时间", summary.get("end_time", "")),
            ("测试时长", summary.get("duration", "")),
            ("总模块数", summary.get("total_modules", 0)),
            ("通过模块数", summary.get("passed_modules", 0)),
            ("失败模块数", summary.get("failed_modules", 0)),
            ("总数据点数", summary.get("total_data_points", 0)),
            ("失败数据点数", summary.get("failed_data_points", 0)),
            ("测试结果", "通过" if summary.get("overall_result", False) else "失败")
        ]
        
        for label, value in summary_items:
            ws[f'A{row}'] = label
            ws[f'B{row}'] = value
            
            # 设置样式
            ws[f'A{row}'].font = Font(bold=True)
            ws[f'A{row}'].alignment = self.left_alignment
            ws[f'B{row}'].alignment = self.left_alignment
            
            # 设置结果行颜色
            if label == "测试结果":
                fill = self.pass_fill if value == "通过" else self.fail_fill
                ws[f'A{row}'].fill = fill
                ws[f'B{row}'].fill = fill
            
            row += 1
    
    def _create_module_data_sheet(self, workbook: Workbook, module_id: str, data_list: List[Dict[str, Any]]):
        """创建模块数据表"""
        if not data_list:
            return
        
        sheet_name = f"数据_{module_id}"[:31]  # Excel工作表名称限制
        ws = workbook.create_sheet(sheet_name)
        
        # 转换为DataFrame
        df = pd.DataFrame(data_list)
        
        # 添加到工作表
        for r in dataframe_to_rows(df, index=False, header=True):
            ws.append(r)
        
        # 设置表头样式
        for cell in ws[1]:
            cell.font = self.header_font
            cell.fill = self.header_fill
            cell.alignment = self.center_alignment
            cell.border = self.border
        
        # 设置数据样式
        for row in ws.iter_rows(min_row=2, max_row=ws.max_row):
            for cell in row:
                cell.font = self.data_font
                cell.alignment = self.center_alignment
                cell.border = self.border
                
                # 根据验证结果设置颜色
                if hasattr(cell, 'value') and cell.value is not None:
                    if str(cell.value).lower() in ['false', 'fail', 'failed']:
                        cell.fill = self.fail_fill
                    elif str(cell.value).lower() in ['true', 'pass', 'passed']:
                        cell.fill = self.pass_fill
        
        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def _create_module_error_sheet(self, workbook: Workbook, module_id: str, error_list: List[Dict[str, Any]]):
        """创建模块错误表"""
        if not error_list:
            return
        
        sheet_name = f"错误_{module_id}"[:31]  # Excel工作表名称限制
        ws = workbook.create_sheet(sheet_name)
        
        # 转换为DataFrame
        df = pd.DataFrame(error_list)
        
        # 添加到工作表
        for r in dataframe_to_rows(df, index=False, header=True):
            ws.append(r)
        
        # 设置表头样式
        for cell in ws[1]:
            cell.font = self.header_font
            cell.fill = self.header_fill
            cell.alignment = self.center_alignment
            cell.border = self.border
        
        # 设置数据样式（错误数据用红色背景）
        for row in ws.iter_rows(min_row=2, max_row=ws.max_row):
            for cell in row:
                cell.font = self.error_font
                cell.fill = self.fail_fill
                cell.alignment = self.center_alignment
                cell.border = self.border
        
        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def update_existing_file(
        self,
        file_path: str,
        sheet_name: str,
        data: List[Dict[str, Any]],
        append: bool = True
    ):
        """更新现有Excel文件"""
        try:
            file_path = Path(file_path)
            
            if file_path.exists():
                wb = load_workbook(file_path)
            else:
                wb = Workbook()
                wb.remove(wb.active)  # 删除默认工作表
            
            # 检查工作表是否存在
            if sheet_name in wb.sheetnames:
                if append:
                    ws = wb[sheet_name]
                    # 追加数据
                    df = pd.DataFrame(data)
                    for r in dataframe_to_rows(df, index=False, header=False):
                        ws.append(r)
                else:
                    # 替换工作表
                    wb.remove(wb[sheet_name])
                    ws = wb.create_sheet(sheet_name)
                    df = pd.DataFrame(data)
                    for r in dataframe_to_rows(df, index=False, header=True):
                        ws.append(r)
            else:
                # 创建新工作表
                ws = wb.create_sheet(sheet_name)
                df = pd.DataFrame(data)
                for r in dataframe_to_rows(df, index=False, header=True):
                    ws.append(r)
            
            # 保存文件
            wb.save(file_path)
            
            self.logger.info(f"Excel文件已更新: {file_path}")
            
        except Exception as e:
            self.logger.error(f"更新Excel文件失败: {file_path}, 错误: {str(e)}")
            raise
    
    def create_module_report(
        self,
        module_id: str,
        data_list: List[Dict[str, Any]],
        error_list: List[Dict[str, Any]],
        result: str,
        filename: Optional[str] = None
    ) -> str:
        """为单个模块创建报告"""
        try:
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"module_report_{module_id}_{timestamp}.xlsx"
            
            file_path = self.output_dir / filename
            
            # 创建工作簿
            wb = Workbook()
            
            # 删除默认工作表
            wb.remove(wb.active)
            
            # 创建数据表
            if data_list:
                self._create_module_data_sheet(wb, module_id, data_list)
            
            # 创建错误表
            if error_list:
                self._create_module_error_sheet(wb, module_id, error_list)
            
            # 创建结果表
            self._create_module_result_sheet(wb, module_id, result, len(data_list), len(error_list))
            
            # 保存文件
            wb.save(file_path)
            
            self.logger.info(f"模块报告已保存: {file_path}")
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"创建模块报告失败: {str(e)}")
            raise
    
    def _create_module_result_sheet(self, workbook: Workbook, module_id: str, result: str, data_count: int, error_count: int):
        """创建模块结果表"""
        ws = workbook.create_sheet("测试结果", 0)
        
        # 设置列宽
        ws.column_dimensions['A'].width = 20
        ws.column_dimensions['B'].width = 30
        
        # 添加标题
        ws['A1'] = f"模块 {module_id} 测试结果"
        ws['A1'].font = Font(name='Arial', size=16, bold=True)
        ws.merge_cells('A1:B1')
        
        # 添加结果信息
        row = 3
        result_items = [
            ("模块ID", module_id),
            ("测试时间", datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
            ("数据点数", data_count),
            ("错误数", error_count),
            ("测试结果", result)
        ]
        
        for label, value in result_items:
            ws[f'A{row}'] = label
            ws[f'B{row}'] = value
            
            # 设置样式
            ws[f'A{row}'].font = Font(bold=True)
            ws[f'A{row}'].alignment = self.left_alignment
            ws[f'B{row}'].alignment = self.left_alignment
            
            # 设置结果行颜色
            if label == "测试结果":
                fill = self.pass_fill if result == "通过" else self.fail_fill
                ws[f'A{row}'].fill = fill
                ws[f'B{row}'].fill = fill
            
            row += 1
    
    def export_config_template(self, filename: str = "config_template.xlsx") -> str:
        """导出配置模板"""
        try:
            file_path = self.output_dir / filename
            
            wb = Workbook()
            wb.remove(wb.active)
            
            # 创建设备配置模板
            self._create_device_config_template(wb)
            
            # 创建模块类型配置模板
            self._create_module_type_config_template(wb)
            
            # 保存文件
            wb.save(file_path)
            
            self.logger.info(f"配置模板已导出: {file_path}")
            return str(file_path)
            
        except Exception as e:
            self.logger.error(f"导出配置模板失败: {str(e)}")
            raise
    
    def _create_device_config_template(self, workbook: Workbook):
        """创建设备配置模板"""
        ws = workbook.create_sheet("设备配置")
        
        # 表头
        headers = [
            "device_id", "device_name", "device_type", "connection_type",
            "host", "port", "username", "password", "timeout", "enabled"
        ]
        
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = self.header_font
            cell.fill = self.header_fill
            cell.alignment = self.center_alignment
            cell.border = self.border
        
        # 示例数据
        example_data = [
            ["oct_001", "OCT设备001", "oct", "ssh", "*************", 22, "admin", "Admin_123", 30, True],
            ["npb_001", "NPB设备001", "npb", "http", "*************", 443, "admin", "Admin_123", 30, True]
        ]
        
        for row, data in enumerate(example_data, 2):
            for col, value in enumerate(data, 1):
                cell = ws.cell(row=row, column=col, value=value)
                cell.font = self.data_font
                cell.alignment = self.center_alignment
                cell.border = self.border
        
        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 30)
            ws.column_dimensions[column_letter].width = adjusted_width
    
    def _create_module_type_config_template(self, workbook: Workbook):
        """创建模块类型配置模板"""
        ws = workbook.create_sheet("模块类型配置")
        
        # 表头
        headers = [
            "module_type", "display_name", "data_fields", "validation_rules", "enabled"
        ]
        
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = self.header_font
            cell.fill = self.header_fill
            cell.alignment = self.center_alignment
            cell.border = self.border
        
        # 示例数据
        example_data = [
            ["cfp2_dco", "CFP2 DCO模块", "sn,mn,Module Temperature,Module Power Supply", "温度:-10~85,电压:3.0~3.6", True]
        ]
        
        for row, data in enumerate(example_data, 2):
            for col, value in enumerate(data, 1):
                cell = ws.cell(row=row, column=col, value=value)
                cell.font = self.data_font
                cell.alignment = self.center_alignment
                cell.border = self.border
        
        # 自动调整列宽
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 40)
            ws.column_dimensions[column_letter].width = adjusted_width
