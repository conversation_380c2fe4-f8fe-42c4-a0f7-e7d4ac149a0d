"""
应用程序信号定义
"""

from PySide6.QtCore import QObject, Signal
from typing import Dict, Any, List


class AppSignals(QObject):
    """应用程序全局信号"""
    
    # 应用程序生命周期信号
    app_started = Signal()
    app_stopping = Signal()
    app_stopped = Signal()
    
    # 配置变更信号
    config_changed = Signal(str, object)  # 配置键, 新值
    
    # 设备管理信号
    device_added = Signal(str)           # 设备ID
    device_removed = Signal(str)         # 设备ID
    device_connected = Signal(str)       # 设备ID
    device_disconnected = Signal(str)    # 设备ID
    device_status_changed = Signal(str, str)  # 设备ID, 状态
    
    # 测试控制信号
    test_started = Signal(str)           # 会话ID
    test_stopped = Signal(str)           # 会话ID
    test_paused = Signal(str)            # 会话ID
    test_resumed = Signal(str)           # 会话ID
    test_completed = Signal(str, bool)   # 会话ID, 是否成功
    
    # 数据更新信号
    module_data_updated = Signal(str, dict)      # 模块ID, 数据
    module_status_changed = Signal(str, str)     # 模块ID, 状态
    validation_result = Signal(str, str, bool)   # 模块ID, 字段名, 是否通过
    
    # UI更新信号
    table_row_added = Signal(str, dict)          # Tab名称, 行数据
    table_row_updated = Signal(str, str, dict)   # Tab名称, 模块ID, 更新数据
    table_row_removed = Signal(str, str)         # Tab名称, 模块ID
    table_row_highlight = Signal(str, str, str)  # Tab名称, 模块ID, 颜色
    
    # 日志和消息信号
    log_message = Signal(str)            # 日志消息
    info_message = Signal(str, str)      # 标题, 消息
    warning_message = Signal(str, str)   # 标题, 消息
    error_message = Signal(str, str)     # 标题, 消息
    
    # 进度更新信号
    progress_updated = Signal(str, int)  # 任务名称, 进度百分比
    progress_message = Signal(str, str)  # 任务名称, 消息
    
    # Excel输出信号
    excel_saved = Signal(str, str)       # 文件路径, 模块ID
    excel_error = Signal(str, str)       # 错误信息, 模块ID


class DeviceSignals(QObject):
    """设备相关信号"""
    
    # 连接状态信号
    connection_status_changed = Signal(str, str)  # 设备ID, 状态
    connection_error = Signal(str, str)           # 设备ID, 错误信息
    
    # 数据接收信号
    data_received = Signal(str, str)              # 设备ID, 数据
    command_executed = Signal(str, str, str)      # 设备ID, 命令, 结果
    
    # OCT设备特定信号
    slot_switched = Signal(str, str)              # 设备ID, 槽位ID
    module_detected = Signal(str, str, bool)      # 设备ID, 槽位ID, 是否检测到
    module_data_read = Signal(str, str, dict)     # 设备ID, 槽位ID, 数据
    
    # NPB设备特定信号
    flow_data_updated = Signal(str, list)         # 设备ID, 流量数据
    port_errors_cleared = Signal(str, str)        # 设备ID, 端口名称
    port_stability_checked = Signal(str, str, bool)  # 设备ID, 端口名称, 是否稳定


class TestSignals(QObject):
    """测试相关信号"""
    
    # 测试会话信号
    session_created = Signal(str)                 # 会话ID
    session_started = Signal(str)                 # 会话ID
    session_stopped = Signal(str)                 # 会话ID
    session_completed = Signal(str, dict)         # 会话ID, 结果
    
    # 测试步骤信号
    step_started = Signal(str, str)               # 会话ID, 步骤名称
    step_completed = Signal(str, str, bool)       # 会话ID, 步骤名称, 是否成功
    step_failed = Signal(str, str, str)           # 会话ID, 步骤名称, 错误信息
    
    # 模块测试信号
    module_test_started = Signal(str, str)        # 会话ID, 模块ID
    module_test_completed = Signal(str, str, bool)  # 会话ID, 模块ID, 是否成功
    module_test_failed = Signal(str, str, str)    # 会话ID, 模块ID, 错误信息
    
    # 数据验证信号
    data_validated = Signal(str, str, dict)       # 会话ID, 模块ID, 验证结果
    validation_failed = Signal(str, str, str, str)  # 会话ID, 模块ID, 字段名, 错误信息
    
    # NPB检查信号
    npb_stability_checked = Signal(str, dict)     # 会话ID, 稳定性结果
    npb_errors_cleared = Signal(str, list)        # 会话ID, 清零的端口列表


class UISignals(QObject):
    """UI相关信号"""
    
    # 窗口管理信号
    window_shown = Signal()
    window_hidden = Signal()
    window_closed = Signal()
    
    # Tab管理信号
    tab_added = Signal(str)                       # Tab名称
    tab_removed = Signal(str)                     # Tab名称
    tab_activated = Signal(str)                   # Tab名称
    tab_data_updated = Signal(str, dict)          # Tab名称, 数据
    
    # 表格管理信号
    table_created = Signal(str, list)             # Tab名称, 列头
    table_cleared = Signal(str)                   # Tab名称
    table_sorted = Signal(str, int, bool)         # Tab名称, 列索引, 升序
    
    # 控件状态信号
    button_enabled = Signal(str, bool)            # 按钮名称, 是否启用
    control_value_changed = Signal(str, object)   # 控件名称, 新值
    
    # 状态栏信号
    status_message = Signal(str)                  # 状态消息
    status_progress = Signal(int)                 # 进度值


# 全局信号实例
app_signals = AppSignals()
device_signals = DeviceSignals()
test_signals = TestSignals()
ui_signals = UISignals()


def get_app_signals() -> AppSignals:
    """获取应用程序信号实例"""
    return app_signals


def get_device_signals() -> DeviceSignals:
    """获取设备信号实例"""
    return device_signals


def get_test_signals() -> TestSignals:
    """获取测试信号实例"""
    return test_signals


def get_ui_signals() -> UISignals:
    """获取UI信号实例"""
    return ui_signals
