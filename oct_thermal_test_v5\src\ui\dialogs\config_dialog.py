"""
配置对话框
"""

from typing import Dict, Any, Optional
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget,
    QFormLayout, QLineEdit, QSpinBox, QDoubleSpinBox,
    QCheckBox, QComboBox, QPushButton, QTextEdit,
    QGroupBox, QLabel, QFileDialog, QMessageBox, QWidget
)
from PySide6.QtCore import Qt, Signal

from src.app.config_manager import ConfigManager
from src.utils.logger import get_logger


class AppConfigTab(QWidget):
    """应用程序配置Tab"""
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        self.config_manager = config_manager
        self.logger = get_logger(__name__)
        
        self._setup_ui()
        self._load_config()
    
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 基本设置组
        basic_group = QGroupBox("基本设置")
        basic_layout = QFormLayout(basic_group)
        
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["DEBUG", "INFO", "WARNING", "ERROR"])
        basic_layout.addRow("日志级别:", self.log_level_combo)
        
        self.loop_time_spin = QSpinBox()
        self.loop_time_spin.setRange(1, 3600)
        self.loop_time_spin.setSuffix(" 秒")
        basic_layout.addRow("循环间隔:", self.loop_time_spin)
        
        self.retry_count_spin = QSpinBox()
        self.retry_count_spin.setRange(0, 10)
        basic_layout.addRow("最大重试次数:", self.retry_count_spin)
        
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(5, 300)
        self.timeout_spin.setSuffix(" 秒")
        basic_layout.addRow("连接超时:", self.timeout_spin)
        
        layout.addWidget(basic_group)
        
        # Excel设置组
        excel_group = QGroupBox("Excel设置")
        excel_layout = QFormLayout(excel_group)
        
        self.auto_save_check = QCheckBox()
        excel_layout.addRow("自动保存:", self.auto_save_check)
        
        self.save_interval_spin = QSpinBox()
        self.save_interval_spin.setRange(10, 3600)
        self.save_interval_spin.setSuffix(" 秒")
        excel_layout.addRow("保存间隔:", self.save_interval_spin)
        
        self.max_rows_spin = QSpinBox()
        self.max_rows_spin.setRange(1000, 100000)
        excel_layout.addRow("最大行数:", self.max_rows_spin)
        
        layout.addWidget(excel_group)
        
        # 测试设置组
        test_group = QGroupBox("测试设置")
        test_layout = QFormLayout(test_group)
        
        self.stability_timeout_spin = QSpinBox()
        self.stability_timeout_spin.setRange(60, 1800)
        self.stability_timeout_spin.setSuffix(" 秒")
        test_layout.addRow("稳定性检查超时:", self.stability_timeout_spin)
        
        self.stability_interval_spin = QSpinBox()
        self.stability_interval_spin.setRange(1, 60)
        self.stability_interval_spin.setSuffix(" 秒")
        test_layout.addRow("稳定性检查间隔:", self.stability_interval_spin)
        
        self.clear_errors_check = QCheckBox()
        test_layout.addRow("测试前清零错误:", self.clear_errors_check)
        
        self.auto_retry_check = QCheckBox()
        test_layout.addRow("失败时自动重试:", self.auto_retry_check)
        
        layout.addWidget(test_group)
        
        layout.addStretch()
    
    def _load_config(self):
        """加载配置"""
        # 基本设置
        log_level = self.config_manager.get_app_config("log_level", "INFO")
        index = self.log_level_combo.findText(log_level)
        if index >= 0:
            self.log_level_combo.setCurrentIndex(index)
        
        self.loop_time_spin.setValue(self.config_manager.get_app_config("single_loop_time", 10))
        self.retry_count_spin.setValue(self.config_manager.get_app_config("max_retry_count", 3))
        self.timeout_spin.setValue(self.config_manager.get_app_config("connection_timeout", 30))
        
        # Excel设置
        self.auto_save_check.setChecked(self.config_manager.get_app_config("excel_settings.auto_save", True))
        self.save_interval_spin.setValue(self.config_manager.get_app_config("excel_settings.save_interval", 60))
        self.max_rows_spin.setValue(self.config_manager.get_app_config("excel_settings.max_rows_per_sheet", 10000))
        
        # 测试设置
        self.stability_timeout_spin.setValue(self.config_manager.get_app_config("test_settings.stability_check_timeout", 300))
        self.stability_interval_spin.setValue(self.config_manager.get_app_config("test_settings.stability_check_interval", 10))
        self.clear_errors_check.setChecked(self.config_manager.get_app_config("test_settings.clear_errors_before_test", True))
        self.auto_retry_check.setChecked(self.config_manager.get_app_config("test_settings.auto_retry_on_failure", True))
    
    def save_config(self):
        """保存配置"""
        # 基本设置
        self.config_manager.set_app_config("log_level", self.log_level_combo.currentText())
        self.config_manager.set_app_config("single_loop_time", self.loop_time_spin.value())
        self.config_manager.set_app_config("max_retry_count", self.retry_count_spin.value())
        self.config_manager.set_app_config("connection_timeout", self.timeout_spin.value())
        
        # Excel设置
        self.config_manager.set_app_config("excel_settings.auto_save", self.auto_save_check.isChecked())
        self.config_manager.set_app_config("excel_settings.save_interval", self.save_interval_spin.value())
        self.config_manager.set_app_config("excel_settings.max_rows_per_sheet", self.max_rows_spin.value())
        
        # 测试设置
        self.config_manager.set_app_config("test_settings.stability_check_timeout", self.stability_timeout_spin.value())
        self.config_manager.set_app_config("test_settings.stability_check_interval", self.stability_interval_spin.value())
        self.config_manager.set_app_config("test_settings.clear_errors_before_test", self.clear_errors_check.isChecked())
        self.config_manager.set_app_config("test_settings.auto_retry_on_failure", self.auto_retry_check.isChecked())


class DeviceConfigTab(QWidget):
    """设备配置Tab"""
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        self.config_manager = config_manager
        self.logger = get_logger(__name__)
        
        self._setup_ui()
    
    def _setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout(self)
        
        # 设备列表
        devices_group = QGroupBox("设备列表")
        devices_layout = QVBoxLayout(devices_group)
        
        # 设备信息显示
        self.devices_text = QTextEdit()
        self.devices_text.setReadOnly(True)
        self.devices_text.setMaximumHeight(200)
        devices_layout.addWidget(self.devices_text)
        
        # 按钮
        buttons_layout = QHBoxLayout()
        
        refresh_button = QPushButton("刷新")
        refresh_button.clicked.connect(self._refresh_devices)
        buttons_layout.addWidget(refresh_button)
        
        import_button = QPushButton("导入配置")
        import_button.clicked.connect(self._import_config)
        buttons_layout.addWidget(import_button)
        
        export_button = QPushButton("导出配置")
        export_button.clicked.connect(self._export_config)
        buttons_layout.addWidget(export_button)
        
        buttons_layout.addStretch()
        devices_layout.addLayout(buttons_layout)
        
        layout.addWidget(devices_group)
        
        # NPB设置组
        npb_group = QGroupBox("NPB设置")
        npb_layout = QFormLayout(npb_group)
        
        self.npb_username_edit = QLineEdit()
        npb_layout.addRow("默认用户名:", self.npb_username_edit)
        
        self.npb_password_edit = QLineEdit()
        self.npb_password_edit.setEchoMode(QLineEdit.Password)
        npb_layout.addRow("默认密码:", self.npb_password_edit)
        
        self.npb_timeout_spin = QSpinBox()
        self.npb_timeout_spin.setRange(5, 300)
        self.npb_timeout_spin.setSuffix(" 秒")
        npb_layout.addRow("请求超时:", self.npb_timeout_spin)
        
        layout.addWidget(npb_group)
        
        # OCT设置组
        oct_group = QGroupBox("OCT设置")
        oct_layout = QFormLayout(oct_group)
        
        self.oct_username_edit = QLineEdit()
        oct_layout.addRow("默认用户名:", self.oct_username_edit)
        
        self.oct_password_edit = QLineEdit()
        self.oct_password_edit.setEchoMode(QLineEdit.Password)
        oct_layout.addRow("默认密码:", self.oct_password_edit)
        
        self.oct_timeout_spin = QSpinBox()
        self.oct_timeout_spin.setRange(5, 60)
        self.oct_timeout_spin.setSuffix(" 秒")
        oct_layout.addRow("命令超时:", self.oct_timeout_spin)
        
        layout.addWidget(oct_group)
        
        layout.addStretch()
        
        # 加载配置
        self._load_config()
        self._refresh_devices()
    
    def _load_config(self):
        """加载配置"""
        # NPB设置
        self.npb_username_edit.setText(self.config_manager.get_app_config("npb_settings.default_username", "admin"))
        self.npb_password_edit.setText(self.config_manager.get_app_config("npb_settings.default_password", "Admin_123"))
        self.npb_timeout_spin.setValue(self.config_manager.get_app_config("npb_settings.request_timeout", 30))
        
        # OCT设置
        self.oct_username_edit.setText(self.config_manager.get_app_config("oct_settings.default_username", "admin"))
        self.oct_password_edit.setText(self.config_manager.get_app_config("oct_settings.default_password", "Admin_123"))
        self.oct_timeout_spin.setValue(self.config_manager.get_app_config("oct_settings.command_timeout", 10))
    
    def _refresh_devices(self):
        """刷新设备列表"""
        device_names = self.config_manager.get_all_device_names()
        
        text = f"已配置设备数量: {len(device_names)}\n\n"
        
        for device_name in device_names:
            device_config = self.config_manager.get_device_config(device_name)
            if device_config:
                text += f"设备: {device_name}\n"
                text += f"  类型: {device_config.get('device_type', 'unknown')}\n"
                text += f"  连接: {device_config.get('connection', {}).get('connection_type', 'unknown')}\n"
                text += f"  地址: {device_config.get('connection', {}).get('host', 'unknown')}\n"
                text += f"  启用: {'是' if device_config.get('enabled', True) else '否'}\n\n"
        
        self.devices_text.setText(text)
    
    def _import_config(self):
        """导入配置"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入设备配置", "", "Excel文件 (*.xlsx);;JSON文件 (*.json)"
        )
        
        if file_path:
            try:
                # TODO: 实现配置导入逻辑
                QMessageBox.information(self, "导入配置", f"配置导入功能待实现\n文件: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "导入失败", f"导入配置失败:\n{str(e)}")
    
    def _export_config(self):
        """导出配置"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出设备配置", "device_config.xlsx", "Excel文件 (*.xlsx);;JSON文件 (*.json)"
        )
        
        if file_path:
            try:
                # TODO: 实现配置导出逻辑
                QMessageBox.information(self, "导出配置", f"配置导出功能待实现\n文件: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "导出失败", f"导出配置失败:\n{str(e)}")
    
    def save_config(self):
        """保存配置"""
        # NPB设置
        self.config_manager.set_app_config("npb_settings.default_username", self.npb_username_edit.text())
        self.config_manager.set_app_config("npb_settings.default_password", self.npb_password_edit.text())
        self.config_manager.set_app_config("npb_settings.request_timeout", self.npb_timeout_spin.value())
        
        # OCT设置
        self.config_manager.set_app_config("oct_settings.default_username", self.oct_username_edit.text())
        self.config_manager.set_app_config("oct_settings.default_password", self.oct_password_edit.text())
        self.config_manager.set_app_config("oct_settings.command_timeout", self.oct_timeout_spin.value())


class ConfigDialog(QDialog):
    """配置对话框"""
    
    config_saved = Signal()  # 配置保存信号
    
    def __init__(self, config_manager: ConfigManager, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.logger = get_logger(__name__)
        
        self._setup_ui()
        self.setModal(True)
    
    def _setup_ui(self):
        """设置UI"""
        self.setWindowTitle("系统配置")
        self.setFixedSize(600, 500)
        
        layout = QVBoxLayout(self)
        
        # Tab控件
        self.tab_widget = QTabWidget()
        
        # 应用程序配置Tab
        self.app_config_tab = AppConfigTab(self.config_manager)
        self.tab_widget.addTab(self.app_config_tab, "应用程序")
        
        # 设备配置Tab
        self.device_config_tab = DeviceConfigTab(self.config_manager)
        self.tab_widget.addTab(self.device_config_tab, "设备")
        
        layout.addWidget(self.tab_widget)
        
        # 按钮
        buttons_layout = QHBoxLayout()
        
        save_button = QPushButton("保存")
        save_button.clicked.connect(self._save_config)
        buttons_layout.addWidget(save_button)
        
        cancel_button = QPushButton("取消")
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)
        
        apply_button = QPushButton("应用")
        apply_button.clicked.connect(self._apply_config)
        buttons_layout.addWidget(apply_button)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
    
    def _save_config(self):
        """保存配置"""
        try:
            self.app_config_tab.save_config()
            self.device_config_tab.save_config()
            
            self.config_saved.emit()
            QMessageBox.information(self, "保存成功", "配置已保存")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存配置失败:\n{str(e)}")
    
    def _apply_config(self):
        """应用配置"""
        try:
            self.app_config_tab.save_config()
            self.device_config_tab.save_config()
            
            self.config_saved.emit()
            QMessageBox.information(self, "应用成功", "配置已应用")
            
        except Exception as e:
            QMessageBox.critical(self, "应用失败", f"应用配置失败:\n{str(e)}")
