"""
日志工具模块
提供统一的日志记录功能
"""

import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional
from logging.handlers import RotatingFileHandler

from PySide6.QtCore import QObject, Signal


class LogHandler(logging.Handler):
    """自定义日志处理器，用于发送日志信号"""
    
    def __init__(self, signal_emitter: Optional[QObject] = None):
        super().__init__()
        self.signal_emitter = signal_emitter
    
    def emit(self, record):
        """发送日志记录"""
        if self.signal_emitter and hasattr(self.signal_emitter, 'log_message'):
            log_entry = self.format(record)
            self.signal_emitter.log_message.emit(log_entry)


class LogSignals(QObject):
    """日志信号类"""
    log_message = Signal(str)  # 日志消息信号
    log_info = Signal(str)     # 信息日志信号
    log_warning = Signal(str)  # 警告日志信号
    log_error = Signal(str)    # 错误日志信号


class Logger:
    """日志管理器"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.signals = LogSignals()
            self.loggers = {}
            self._setup_logging()
            Logger._initialized = True
    
    def _setup_logging(self):
        """设置日志配置"""
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 日志格式
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)s | %(name)s:%(funcName)s:%(lineno)d | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
        
        # 文件处理器 - 所有日志
        all_log_file = log_dir / f"app_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = RotatingFileHandler(
            all_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
        
        # 错误日志文件处理器
        error_log_file = log_dir / f"error_{datetime.now().strftime('%Y%m%d')}.log"
        error_handler = RotatingFileHandler(
            error_log_file,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        root_logger.addHandler(error_handler)
        
        # Qt信号处理器
        signal_handler = LogHandler(self.signals)
        signal_handler.setLevel(logging.INFO)
        signal_handler.setFormatter(formatter)
        root_logger.addHandler(signal_handler)
        
        # 连接信号到具体的日志级别信号
        self.signals.log_message.connect(self._emit_level_specific_signal)
    
    def _emit_level_specific_signal(self, message: str):
        """根据日志级别发送对应信号"""
        if "ERROR" in message:
            self.signals.log_error.emit(message)
        elif "WARNING" in message:
            self.signals.log_warning.emit(message)
        else:
            self.signals.log_info.emit(message)
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取日志记录器"""
        if name not in self.loggers:
            logger = logging.getLogger(name)
            self.loggers[name] = logger
        return self.loggers[name]
    
    def set_level(self, level: str):
        """设置日志级别"""
        level_map = {
            'DEBUG': logging.DEBUG,
            'INFO': logging.INFO,
            'WARNING': logging.WARNING,
            'ERROR': logging.ERROR,
            'CRITICAL': logging.CRITICAL
        }
        
        if level.upper() in level_map:
            logging.getLogger().setLevel(level_map[level.upper()])
            for logger in self.loggers.values():
                logger.setLevel(level_map[level.upper()])
    
    def add_file_handler(self, name: str, filename: str, level: str = 'INFO'):
        """为特定模块添加文件处理器"""
        logger = self.get_logger(name)
        
        # 创建文件处理器
        log_dir = Path("logs")
        log_file = log_dir / filename
        
        handler = RotatingFileHandler(
            log_file,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3,
            encoding='utf-8'
        )
        
        # 设置格式和级别
        formatter = logging.Formatter(
            '%(asctime)s | %(levelname)s | %(name)s:%(funcName)s:%(lineno)d | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        
        level_map = {
            'DEBUG': logging.DEBUG,
            'INFO': logging.INFO,
            'WARNING': logging.WARNING,
            'ERROR': logging.ERROR,
            'CRITICAL': logging.CRITICAL
        }
        handler.setLevel(level_map.get(level.upper(), logging.INFO))
        
        logger.addHandler(handler)


# 全局日志管理器实例
_logger_manager = Logger()


def get_logger(name: str) -> logging.Logger:
    """获取日志记录器"""
    return _logger_manager.get_logger(name)


def get_log_signals() -> LogSignals:
    """获取日志信号对象"""
    return _logger_manager.signals


def set_log_level(level: str):
    """设置日志级别"""
    _logger_manager.set_level(level)


def add_module_log_file(module_name: str, filename: str, level: str = 'INFO'):
    """为模块添加专用日志文件"""
    _logger_manager.add_file_handler(module_name, filename, level)


# 便捷函数
def log_info(message: str, logger_name: str = "app"):
    """记录信息日志"""
    logger = get_logger(logger_name)
    logger.info(message)


def log_warning(message: str, logger_name: str = "app"):
    """记录警告日志"""
    logger = get_logger(logger_name)
    logger.warning(message)


def log_error(message: str, logger_name: str = "app"):
    """记录错误日志"""
    logger = get_logger(logger_name)
    logger.error(message)


def log_debug(message: str, logger_name: str = "app"):
    """记录调试日志"""
    logger = get_logger(logger_name)
    logger.debug(message)
