"""
OCT设备实现
"""

import re
from typing import Dict, Any, List, Optional
from .base import Device, DeviceConnection
from src.models.device_config import DeviceConfig, SlotConfig


class OCTDevice(Device):
    """OCT设备实现"""
    
    def __init__(self, device_config: DeviceConfig, connection: DeviceConnection):
        super().__init__(device_config.device_id, connection)
        self.config = device_config
        self.current_slot: Optional[str] = None
        self._device_info: Dict[str, Any] = {}
        self._slot_modules: Dict[str, List[str]] = {}  # 槽位 -> 模块列表
    
    def initialize(self) -> bool:
        """初始化OCT设备"""
        try:
            self.logger.info("正在初始化OCT设备...")
            
            # 发送系统命令进入系统模式
            result = self.send_command("sys")
            
            # 检查是否成功进入系统模式
            if "[Accelink]" in result:
                self.logger.info("OCT设备初始化成功")
                return True
            else:
                self.logger.error(f"OCT设备初始化失败，响应: {result}")
                return False
                
        except Exception as e:
            self.logger.error(f"OCT设备初始化异常: {str(e)}")
            return False
    
    def get_device_info(self) -> Dict[str, Any]:
        """获取设备信息"""
        if not self._device_info:
            try:
                # 获取设备基本信息
                result = self.send_command("show version")
                self._device_info = self._parse_device_info(result)
            except Exception as e:
                self.logger.error(f"获取设备信息失败: {str(e)}")
                self._device_info = {"error": str(e)}
        
        return self._device_info
    
    def check_health(self) -> bool:
        """检查设备健康状态"""
        try:
            # 发送简单命令检查设备响应
            result = self.send_command("sys")
            return "[Accelink]" in result
        except Exception as e:
            self.logger.error(f"设备健康检查失败: {str(e)}")
            return False
    
    def switch_to_slot(self, slot_id: str) -> bool:
        """切换到指定槽位"""
        try:
            self.logger.info(f"切换到槽位: {slot_id}")
            
            # 发送槽位切换命令
            command = f"slot {slot_id}"
            result = self.send_command(command)
            
            # 检查切换是否成功
            if "Error" not in result and "error" not in result:
                self.current_slot = slot_id
                self.logger.info(f"成功切换到槽位: {slot_id}")
                return True
            else:
                self.logger.error(f"切换槽位失败: {slot_id}, 响应: {result}")
                return False
                
        except Exception as e:
            self.logger.error(f"切换槽位异常: {slot_id}, 错误: {str(e)}")
            return False
    
    def check_module_exists(self, slot_id: Optional[str] = None) -> Dict[str, bool]:
        """检查模块是否存在"""
        results = {}
        
        try:
            # 如果指定了槽位，只检查该槽位
            if slot_id:
                slots_to_check = [slot_id]
            else:
                # 检查所有启用的槽位
                slots_to_check = [slot.slot_id for slot in self.config.get_enabled_slots()]
            
            for slot in slots_to_check:
                # 切换到槽位
                if self.switch_to_slot(slot):
                    # 检查模块是否存在
                    result = self.send_command("show module")
                    exists = self._parse_module_exists(result)
                    results[slot] = exists
                    
                    if exists:
                        self.logger.info(f"槽位 {slot} 检测到模块")
                    else:
                        self.logger.warning(f"槽位 {slot} 未检测到模块")
                else:
                    results[slot] = False
                    
        except Exception as e:
            self.logger.error(f"检查模块存在性异常: {str(e)}")
        
        return results
    
    def read_module_data(self, slot_id: str, data_type: str = "cfp2_dco") -> Dict[str, Any]:
        """读取模块数据"""
        try:
            # 切换到指定槽位
            if not self.switch_to_slot(slot_id):
                raise RuntimeError(f"无法切换到槽位: {slot_id}")
            
            # 根据数据类型选择命令
            if data_type == "cfp2_dco":
                command = "show online-display cfp2-dco-info"
            else:
                command = f"show online-display {data_type}-info"
            
            # 发送命令获取数据
            result = self.send_command(command)
            
            # 解析数据
            parsed_data = self._parse_module_data(result, data_type)
            parsed_data["slot_id"] = slot_id
            parsed_data["data_type"] = data_type
            
            return parsed_data
            
        except Exception as e:
            self.logger.error(f"读取模块数据失败: 槽位={slot_id}, 类型={data_type}, 错误={str(e)}")
            return {"error": str(e), "slot_id": slot_id, "data_type": data_type}
    
    def read_all_modules_data(self, data_type: str = "cfp2_dco") -> Dict[str, Dict[str, Any]]:
        """读取所有模块数据"""
        all_data = {}
        
        # 获取启用的槽位
        enabled_slots = self.config.get_enabled_slots()
        
        for slot_config in enabled_slots:
            slot_id = slot_config.slot_id
            
            # 如果槽位指定了模块类型，使用指定的类型
            module_type = slot_config.module_type if slot_config.module_type else data_type
            
            try:
                data = self.read_module_data(slot_id, module_type)
                all_data[slot_id] = data
            except Exception as e:
                self.logger.error(f"读取槽位 {slot_id} 数据失败: {str(e)}")
                all_data[slot_id] = {"error": str(e)}
        
        return all_data
    
    def get_serial_number(self, slot_id: str) -> str:
        """获取模块序列号"""
        try:
            data = self.read_module_data(slot_id)
            return self._extract_serial_number(data)
        except Exception as e:
            self.logger.error(f"获取序列号失败: 槽位={slot_id}, 错误={str(e)}")
            return ""
    
    def _parse_device_info(self, result: str) -> Dict[str, Any]:
        """解析设备信息"""
        info = {}
        
        try:
            lines = result.split('\n')
            for line in lines:
                line = line.strip()
                if ':' in line:
                    key, value = line.split(':', 1)
                    info[key.strip()] = value.strip()
        except Exception as e:
            self.logger.error(f"解析设备信息失败: {str(e)}")
        
        return info
    
    def _parse_module_exists(self, result: str) -> bool:
        """解析模块是否存在"""
        # 检查结果中是否包含模块信息
        if "No module" in result or "not found" in result or "Error" in result:
            return False
        
        # 检查是否有有效的模块数据
        if any(keyword in result for keyword in ["Serial", "Model", "Temperature", "Power"]):
            return True
        
        return False
    
    def _parse_module_data(self, result: str, data_type: str) -> Dict[str, Any]:
        """解析模块数据"""
        data = {}
        
        try:
            lines = result.split('\n')
            
            for line in lines:
                line = line.strip()
                if not line or line.startswith('-') or line.startswith('='):
                    continue
                
                # 解析键值对
                if ':' in line:
                    parts = line.split(':', 1)
                    if len(parts) == 2:
                        key = parts[0].strip()
                        value = parts[1].strip()
                        
                        # 尝试转换数值
                        data[key] = self._convert_value(value)
                
                # 解析表格形式的数据
                elif '|' in line:
                    parts = [part.strip() for part in line.split('|')]
                    if len(parts) >= 2:
                        key = parts[0]
                        value = parts[1] if len(parts) > 1 else ""
                        data[key] = self._convert_value(value)
        
        except Exception as e:
            self.logger.error(f"解析模块数据失败: {str(e)}")
            data["parse_error"] = str(e)
        
        return data
    
    def _convert_value(self, value: str) -> Any:
        """转换值的类型"""
        if not value or value == "N/A" or value == "--":
            return None
        
        # 尝试转换为数字
        try:
            # 处理十六进制
            if value.startswith('0x') or value.startswith('0X'):
                return int(value, 16)
            
            # 尝试转换为整数
            if '.' not in value:
                return int(value)
            else:
                return float(value)
        except ValueError:
            pass
        
        # 返回字符串
        return value
    
    def _extract_serial_number(self, data: Dict[str, Any]) -> str:
        """从数据中提取序列号"""
        # 常见的序列号字段名
        sn_fields = ["sn", "Serial Number", "SerialNumber", "serial_number", "Serial"]
        
        for field in sn_fields:
            if field in data and data[field]:
                return str(data[field])
        
        return ""
    
    def get_slot_config(self, slot_id: str) -> Optional[SlotConfig]:
        """获取槽位配置"""
        return self.config.get_slot_by_id(slot_id)
    
    def get_enabled_slots(self) -> List[SlotConfig]:
        """获取启用的槽位"""
        return self.config.get_enabled_slots()
    
    def send_exit_command(self):
        """发送退出命令"""
        try:
            self.send_command("exit")
        except Exception as e:
            self.logger.error(f"发送退出命令失败: {str(e)}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取设备状态"""
        status = super().get_status()
        status.update({
            "device_type": "oct",
            "current_slot": self.current_slot,
            "total_slots": len(self.config.slots),
            "enabled_slots": len(self.config.get_enabled_slots()),
            "device_info": self._device_info
        })
        return status
