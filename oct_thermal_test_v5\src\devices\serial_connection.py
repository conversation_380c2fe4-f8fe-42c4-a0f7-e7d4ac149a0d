"""
串口连接实现
"""

import time
import threading
from queue import Queue, Empty
from typing import Optional

try:
    import serial
except ImportError:
    serial = None

from .base import DeviceConnection, ConnectionStatus


class SerialConnection(DeviceConnection):
    """串口连接实现"""
    
    def __init__(
        self,
        connection_id: str,
        port: str,
        baudrate: int = 9600,
        timeout: int = 10,
        bytesize: int = 8,
        parity: str = 'N',
        stopbits: int = 1,
        read_timeout: float = 0.1
    ):
        super().__init__(connection_id)
        
        if serial is None:
            raise ImportError("pyserial库未安装，请运行: pip install pyserial")
        
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.bytesize = bytesize
        self.parity = parity
        self.stopbits = stopbits
        self.read_timeout = read_timeout
        
        self._serial: Optional[serial.Serial] = None
        self._receive_thread: Optional[threading.Thread] = None
        self._response_queue = Queue()
        self._stop_receive = False
    
    def connect(self) -> bool:
        """建立串口连接"""
        try:
            self._set_status(ConnectionStatus.CONNECTING)
            
            # 创建串口连接
            self._serial = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=self.timeout,
                bytesize=self.bytesize,
                parity=self.parity,
                stopbits=self.stopbits
            )
            
            # 清空缓冲区
            self._serial.reset_input_buffer()
            self._serial.reset_output_buffer()
            
            # 启动接收线程
            self._stop_receive = False
            self._receive_thread = threading.Thread(target=self._receive_loop, daemon=True)
            self._receive_thread.start()
            
            self._set_status(ConnectionStatus.CONNECTED)
            self.logger.info(f"串口连接成功: {self.port}")
            return True
            
        except serial.SerialException as e:
            error_msg = f"串口连接失败: {self.port}, 错误: {str(e)}"
            self._set_status(ConnectionStatus.ERROR, error_msg)
            return False
            
        except Exception as e:
            error_msg = f"串口连接异常: {self.port}, 错误: {str(e)}"
            self._set_status(ConnectionStatus.ERROR, error_msg)
            return False
    
    def disconnect(self):
        """断开串口连接"""
        try:
            # 停止接收线程
            self._stop_receive = True
            if self._receive_thread and self._receive_thread.is_alive():
                self._receive_thread.join(timeout=2)
            
            # 关闭串口
            if self._serial and self._serial.is_open:
                self._serial.close()
            
            self._serial = None
            self._receive_thread = None
            
            self._set_status(ConnectionStatus.DISCONNECTED)
            self.logger.info(f"串口连接已断开: {self.port}")
            
        except Exception as e:
            self.logger.error(f"断开串口连接时发生错误: {str(e)}")
    
    def send_command(self, command: str, timeout: Optional[int] = None) -> str:
        """发送命令并获取响应"""
        if not self.is_connected or not self._serial:
            raise ConnectionError("串口连接未建立")
        
        try:
            if timeout is None:
                timeout = self.timeout
            
            # 清空响应队列
            while not self._response_queue.empty():
                try:
                    self._response_queue.get_nowait()
                except Empty:
                    break
            
            # 发送命令
            command_bytes = f'{command}\r\n'.encode('utf-8')
            self._serial.write(command_bytes)
            self._serial.flush()
            
            # 读取响应，处理'More'提示符
            response = self._read_response_with_prompt(timeout)
            
            self.data_received.emit(response)
            return response
            
        except Exception as e:
            error_msg = f"发送串口命令失败: {command}, 错误: {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)
    
    def _receive_loop(self):
        """接收数据循环"""
        buffer = ""
        
        while not self._stop_receive and self._serial and self._serial.is_open:
            try:
                if self._serial.in_waiting > 0:
                    data = self._serial.read(self._serial.in_waiting)
                    
                    try:
                        decoded_data = data.decode('utf-8', errors='ignore')
                        buffer += decoded_data
                        
                        # 检查是否有完整的行
                        while '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                            if line.strip():
                                self._response_queue.put(line.strip())
                    
                    except UnicodeDecodeError:
                        self.logger.warning(f"收到无法解码的数据: {data.hex()}")
                        buffer = ""
                
            except Exception as e:
                if not self._stop_receive:
                    self.logger.error(f"接收数据错误: {e}")
                break
            
            time.sleep(0.01)
    
    def _read_response_with_prompt(self, timeout: int) -> str:
        """读取响应，处理'More'提示符"""
        response_lines = []
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                line = self._response_queue.get(timeout=0.1)
                response_lines.append(line)
                
                # 检查是否遇到'More'提示符
                if 'More' in line or '--More--' in line:
                    # 发送空格继续显示
                    self._serial.write(b' ')
                    self._serial.flush()
                    continue
                
                # 检查是否是命令结束标志
                if any(prompt in line for prompt in ['[', ']', '>', '#']):
                    # 等待一下看是否还有更多数据
                    time.sleep(0.2)
                    if self._response_queue.empty():
                        break
                
            except Empty:
                # 如果已经有响应数据，检查是否结束
                if response_lines:
                    time.sleep(0.1)
                    if self._response_queue.empty():
                        break
                continue
        
        return '\n'.join(response_lines)
    
    def is_alive(self) -> bool:
        """检查连接是否存活"""
        if not self._serial:
            return False
        
        try:
            return self._serial.is_open
        except Exception:
            return False
    
    def get_available_ports(self) -> list:
        """获取可用的串口列表"""
        if serial is None:
            return []
        
        try:
            from serial.tools import list_ports
            ports = list_ports.comports()
            return [port.device for port in ports]
        except ImportError:
            self.logger.warning("无法获取串口列表，请安装完整的pyserial库")
            return []
    
    def set_baudrate(self, baudrate: int):
        """设置波特率"""
        if self._serial and self._serial.is_open:
            self._serial.baudrate = baudrate
            self.baudrate = baudrate
            self.logger.info(f"波特率已设置为: {baudrate}")
    
    def set_timeout(self, timeout: int):
        """设置超时时间"""
        if self._serial:
            self._serial.timeout = timeout
        self.timeout = timeout
        self.logger.info(f"超时时间已设置为: {timeout}秒")
    
    def flush_buffers(self):
        """清空缓冲区"""
        if self._serial and self._serial.is_open:
            self._serial.reset_input_buffer()
            self._serial.reset_output_buffer()
            
            # 清空响应队列
            while not self._response_queue.empty():
                try:
                    self._response_queue.get_nowait()
                except Empty:
                    break
            
            self.logger.debug("串口缓冲区已清空")
    
    def get_connection_info(self) -> dict:
        """获取连接信息"""
        info = super().get_connection_info()
        info.update({
            "connection_type": "serial",
            "port": self.port,
            "baudrate": self.baudrate,
            "timeout": self.timeout,
            "bytesize": self.bytesize,
            "parity": self.parity,
            "stopbits": self.stopbits
        })
        return info
