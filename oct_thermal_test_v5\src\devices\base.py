"""
设备连接基类
定义统一的设备连接接口
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from enum import Enum
from PySide6.QtCore import QObject, Signal

from src.utils.logger import get_logger


class ConnectionStatus(Enum):
    """连接状态枚举"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    ERROR = "error"


class DeviceConnection(QObject):
    """设备连接抽象基类"""
    
    # 连接状态变化信号
    status_changed = Signal(str)  # 连接状态
    data_received = Signal(str)   # 接收到数据
    error_occurred = Signal(str)  # 发生错误
    
    def __init__(self, connection_id: str):
        super().__init__()
        self.connection_id = connection_id
        self.logger = get_logger(f"connection.{connection_id}")
        self._status = ConnectionStatus.DISCONNECTED
        self._last_error: Optional[str] = None
    
    @property
    def status(self) -> ConnectionStatus:
        """获取连接状态"""
        return self._status
    
    @property
    def is_connected(self) -> bool:
        """是否已连接"""
        return self._status == ConnectionStatus.CONNECTED
    
    @property
    def last_error(self) -> Optional[str]:
        """获取最后一次错误"""
        return self._last_error
    
    def _set_status(self, status: ConnectionStatus, error_msg: Optional[str] = None):
        """设置连接状态"""
        old_status = self._status
        self._status = status
        
        if error_msg:
            self._last_error = error_msg
            self.logger.error(f"连接状态变更: {old_status.value} -> {status.value}, 错误: {error_msg}")
            self.error_occurred.emit(error_msg)
        else:
            self.logger.info(f"连接状态变更: {old_status.value} -> {status.value}")
        
        self.status_changed.emit(status.value)
    
    @abstractmethod
    def connect(self) -> bool:
        """建立连接"""
        pass
    
    @abstractmethod
    def disconnect(self):
        """断开连接"""
        pass
    
    @abstractmethod
    def send_command(self, command: str, timeout: Optional[int] = None) -> str:
        """发送命令并获取响应"""
        pass
    
    @abstractmethod
    def is_alive(self) -> bool:
        """检查连接是否存活"""
        pass
    
    def reconnect(self) -> bool:
        """重新连接"""
        self.logger.info("尝试重新连接...")
        self.disconnect()
        return self.connect()
    
    def get_connection_info(self) -> Dict[str, Any]:
        """获取连接信息"""
        return {
            "connection_id": self.connection_id,
            "status": self._status.value,
            "last_error": self._last_error
        }


class Device(QObject):
    """设备抽象基类"""
    
    # 设备状态信号
    device_status_changed = Signal(str, str)  # 设备ID, 状态
    data_updated = Signal(str, dict)          # 设备ID, 数据
    command_executed = Signal(str, str, str)  # 设备ID, 命令, 结果
    
    def __init__(self, device_id: str, connection: DeviceConnection):
        super().__init__()
        self.device_id = device_id
        self.connection = connection
        self.logger = get_logger(f"device.{device_id}")
        
        # 连接信号
        self.connection.status_changed.connect(self._on_connection_status_changed)
        self.connection.error_occurred.connect(self._on_connection_error)
    
    @property
    def is_connected(self) -> bool:
        """是否已连接"""
        return self.connection.is_connected
    
    def _on_connection_status_changed(self, status: str):
        """连接状态变化处理"""
        self.device_status_changed.emit(self.device_id, status)
        self.logger.info(f"设备连接状态变更: {status}")
    
    def _on_connection_error(self, error: str):
        """连接错误处理"""
        self.logger.error(f"设备连接错误: {error}")
    
    def connect(self) -> bool:
        """连接设备"""
        self.logger.info("正在连接设备...")
        return self.connection.connect()
    
    def disconnect(self):
        """断开设备连接"""
        self.logger.info("正在断开设备连接...")
        self.connection.disconnect()
    
    def send_command(self, command: str, timeout: Optional[int] = None) -> str:
        """发送命令"""
        if not self.is_connected:
            raise ConnectionError(f"设备 {self.device_id} 未连接")
        
        self.logger.debug(f"发送命令: {command}")
        result = self.connection.send_command(command, timeout)
        self.command_executed.emit(self.device_id, command, result)
        return result
    
    @abstractmethod
    def initialize(self) -> bool:
        """初始化设备"""
        pass
    
    @abstractmethod
    def get_device_info(self) -> Dict[str, Any]:
        """获取设备信息"""
        pass
    
    @abstractmethod
    def check_health(self) -> bool:
        """检查设备健康状态"""
        pass
    
    def get_status(self) -> Dict[str, Any]:
        """获取设备状态"""
        return {
            "device_id": self.device_id,
            "connection_status": self.connection.status.value,
            "is_connected": self.is_connected,
            "last_error": self.connection.last_error
        }


class DeviceManager(QObject):
    """设备管理器"""
    
    # 设备管理信号
    device_added = Signal(str)      # 设备ID
    device_removed = Signal(str)    # 设备ID
    device_connected = Signal(str)  # 设备ID
    device_disconnected = Signal(str)  # 设备ID
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger("device_manager")
        self._devices: Dict[str, Device] = {}
    
    def add_device(self, device: Device):
        """添加设备"""
        device_id = device.device_id
        if device_id in self._devices:
            self.logger.warning(f"设备 {device_id} 已存在，将被替换")
        
        self._devices[device_id] = device
        
        # 连接设备信号
        device.device_status_changed.connect(self._on_device_status_changed)
        
        self.device_added.emit(device_id)
        self.logger.info(f"设备已添加: {device_id}")
    
    def remove_device(self, device_id: str):
        """移除设备"""
        if device_id in self._devices:
            device = self._devices[device_id]
            device.disconnect()
            del self._devices[device_id]
            
            self.device_removed.emit(device_id)
            self.logger.info(f"设备已移除: {device_id}")
    
    def get_device(self, device_id: str) -> Optional[Device]:
        """获取设备"""
        return self._devices.get(device_id)
    
    def get_all_devices(self) -> List[Device]:
        """获取所有设备"""
        return list(self._devices.values())
    
    def get_connected_devices(self) -> List[Device]:
        """获取已连接的设备"""
        return [device for device in self._devices.values() if device.is_connected]
    
    def connect_device(self, device_id: str) -> bool:
        """连接设备"""
        device = self.get_device(device_id)
        if device:
            return device.connect()
        else:
            self.logger.error(f"设备不存在: {device_id}")
            return False
    
    def disconnect_device(self, device_id: str):
        """断开设备连接"""
        device = self.get_device(device_id)
        if device:
            device.disconnect()
        else:
            self.logger.error(f"设备不存在: {device_id}")
    
    def connect_all_devices(self) -> Dict[str, bool]:
        """连接所有设备"""
        results = {}
        for device_id, device in self._devices.items():
            results[device_id] = device.connect()
        return results
    
    def disconnect_all_devices(self):
        """断开所有设备连接"""
        for device in self._devices.values():
            device.disconnect()
        self.logger.info("所有设备已断开连接")
    
    def _on_device_status_changed(self, device_id: str, status: str):
        """设备状态变化处理"""
        if status == ConnectionStatus.CONNECTED.value:
            self.device_connected.emit(device_id)
        elif status == ConnectionStatus.DISCONNECTED.value:
            self.device_disconnected.emit(device_id)
    
    def get_devices_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有设备状态"""
        return {
            device_id: device.get_status()
            for device_id, device in self._devices.items()
        }
