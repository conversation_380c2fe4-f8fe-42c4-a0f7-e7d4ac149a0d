"""
数据验证工具
"""

import re
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

from src.models.module_info import ValidationRule, ValidationResult
from src.utils.logger import get_logger


class DataValidator:
    """数据验证器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def validate_value(self, value: Any, rule: ValidationRule) -> ValidationResult:
        """验证单个值"""
        if not rule.enabled:
            return ValidationResult.PASS
        
        try:
            # 处理空值
            if value is None or value == "" or value == "N/A":
                return ValidationResult.WARNING
            
            # 根据规则类型进行验证
            if rule.rule_type == "range":
                return self._validate_range(value, rule)
            elif rule.rule_type == "exact":
                return self._validate_exact(value, rule)
            elif rule.rule_type == "increase":
                return self._validate_increase(value, rule)
            elif rule.rule_type == "regex":
                return self._validate_regex(value, rule)
            elif rule.rule_type == "custom":
                return self._validate_custom(value, rule)
            else:
                self.logger.warning(f"未知的验证规则类型: {rule.rule_type}")
                return ValidationResult.WARNING
                
        except Exception as e:
            self.logger.error(f"验证值时发生错误: {str(e)}")
            return ValidationResult.FAIL
    
    def _validate_range(self, value: Any, rule: ValidationRule) -> ValidationResult:
        """验证范围"""
        try:
            # 转换为数值
            numeric_value = self._to_numeric(value)
            if numeric_value is None:
                return ValidationResult.FAIL
            
            # 检查最小值
            if rule.min_value is not None and numeric_value < rule.min_value:
                return ValidationResult.FAIL
            
            # 检查最大值
            if rule.max_value is not None and numeric_value > rule.max_value:
                return ValidationResult.FAIL
            
            # 检查容差范围
            if rule.expected_value is not None and rule.tolerance is not None:
                expected = self._to_numeric(rule.expected_value)
                if expected is not None:
                    diff = abs(numeric_value - expected)
                    if diff > rule.tolerance:
                        return ValidationResult.FAIL
            
            return ValidationResult.PASS
            
        except Exception as e:
            self.logger.error(f"范围验证失败: {str(e)}")
            return ValidationResult.FAIL
    
    def _validate_exact(self, value: Any, rule: ValidationRule) -> ValidationResult:
        """验证精确值"""
        try:
            if rule.expected_value is None:
                return ValidationResult.WARNING
            
            # 字符串比较
            if isinstance(rule.expected_value, str):
                return ValidationResult.PASS if str(value) == rule.expected_value else ValidationResult.FAIL
            
            # 数值比较
            numeric_value = self._to_numeric(value)
            expected_numeric = self._to_numeric(rule.expected_value)
            
            if numeric_value is not None and expected_numeric is not None:
                if rule.tolerance is not None:
                    # 带容差的精确比较
                    diff = abs(numeric_value - expected_numeric)
                    return ValidationResult.PASS if diff <= rule.tolerance else ValidationResult.FAIL
                else:
                    # 严格精确比较
                    return ValidationResult.PASS if numeric_value == expected_numeric else ValidationResult.FAIL
            
            # 直接比较
            return ValidationResult.PASS if value == rule.expected_value else ValidationResult.FAIL
            
        except Exception as e:
            self.logger.error(f"精确值验证失败: {str(e)}")
            return ValidationResult.FAIL
    
    def _validate_increase(self, value: Any, rule: ValidationRule, previous_value: Optional[Any] = None) -> ValidationResult:
        """验证递增"""
        try:
            # 递增验证需要历史数据，这里只做基本检查
            numeric_value = self._to_numeric(value)
            if numeric_value is None:
                return ValidationResult.FAIL
            
            # 如果有期望值，检查是否大于等于期望值
            if rule.expected_value is not None:
                expected = self._to_numeric(rule.expected_value)
                if expected is not None and numeric_value < expected:
                    return ValidationResult.FAIL
            
            # 如果有前一个值，检查是否递增
            if previous_value is not None:
                previous_numeric = self._to_numeric(previous_value)
                if previous_numeric is not None and numeric_value < previous_numeric:
                    return ValidationResult.FAIL
            
            return ValidationResult.PASS
            
        except Exception as e:
            self.logger.error(f"递增验证失败: {str(e)}")
            return ValidationResult.FAIL
    
    def _validate_regex(self, value: Any, rule: ValidationRule) -> ValidationResult:
        """验证正则表达式"""
        try:
            if rule.expected_value is None:
                return ValidationResult.WARNING
            
            pattern = str(rule.expected_value)
            text = str(value)
            
            if re.match(pattern, text):
                return ValidationResult.PASS
            else:
                return ValidationResult.FAIL
                
        except Exception as e:
            self.logger.error(f"正则表达式验证失败: {str(e)}")
            return ValidationResult.FAIL
    
    def _validate_custom(self, value: Any, rule: ValidationRule) -> ValidationResult:
        """自定义验证"""
        try:
            # 这里可以实现自定义验证逻辑
            # 例如，根据rule.description中的自定义规则进行验证
            
            # 示例：检查序列号格式
            if "serial_number" in rule.field_name.lower():
                return self._validate_serial_number(value)
            
            # 示例：检查MAC地址格式
            if "mac" in rule.field_name.lower():
                return self._validate_mac_address(value)
            
            # 默认通过
            return ValidationResult.PASS
            
        except Exception as e:
            self.logger.error(f"自定义验证失败: {str(e)}")
            return ValidationResult.FAIL
    
    def _validate_serial_number(self, value: Any) -> ValidationResult:
        """验证序列号格式"""
        try:
            sn = str(value).strip()
            
            # 检查长度
            if len(sn) < 6 or len(sn) > 20:
                return ValidationResult.FAIL
            
            # 检查字符（只允许字母数字）
            if not re.match(r'^[A-Za-z0-9]+$', sn):
                return ValidationResult.FAIL
            
            return ValidationResult.PASS
            
        except Exception:
            return ValidationResult.FAIL
    
    def _validate_mac_address(self, value: Any) -> ValidationResult:
        """验证MAC地址格式"""
        try:
            mac = str(value).strip().upper()
            
            # 标准MAC地址格式
            mac_pattern = r'^([0-9A-F]{2}[:-]){5}([0-9A-F]{2})$'
            
            if re.match(mac_pattern, mac):
                return ValidationResult.PASS
            else:
                return ValidationResult.FAIL
                
        except Exception:
            return ValidationResult.FAIL
    
    def _to_numeric(self, value: Any) -> Optional[float]:
        """转换为数值"""
        if value is None:
            return None
        
        try:
            # 如果已经是数值
            if isinstance(value, (int, float)):
                return float(value)
            
            # 字符串转换
            str_value = str(value).strip()
            
            # 处理空字符串
            if not str_value or str_value.lower() in ['n/a', 'null', 'none', '--']:
                return None
            
            # 处理十六进制
            if str_value.startswith('0x') or str_value.startswith('0X'):
                return float(int(str_value, 16))
            
            # 处理百分比
            if str_value.endswith('%'):
                return float(str_value[:-1])
            
            # 处理单位（如dBm, V, A等）
            # 移除常见单位后缀
            units = ['dBm', 'dB', 'V', 'A', 'W', 'Hz', 'C', 'F', 'K']
            for unit in units:
                if str_value.upper().endswith(unit.upper()):
                    str_value = str_value[:-len(unit)].strip()
                    break
            
            # 尝试转换为浮点数
            return float(str_value)
            
        except (ValueError, TypeError):
            return None
    
    def validate_data_dict(
        self,
        data: Dict[str, Any],
        rules: List[ValidationRule],
        previous_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, ValidationResult]:
        """验证数据字典"""
        results = {}
        
        for rule in rules:
            field_name = rule.field_name
            
            if field_name in data:
                value = data[field_name]
                previous_value = previous_data.get(field_name) if previous_data else None
                
                if rule.rule_type == "increase" and previous_value is not None:
                    result = self._validate_increase(value, rule, previous_value)
                else:
                    result = self.validate_value(value, rule)
                
                results[field_name] = result
            else:
                # 字段不存在
                results[field_name] = ValidationResult.WARNING
        
        return results
    
    def get_validation_summary(self, results: Dict[str, ValidationResult]) -> Dict[str, int]:
        """获取验证结果汇总"""
        summary = {
            "total": len(results),
            "pass": 0,
            "fail": 0,
            "warning": 0
        }
        
        for result in results.values():
            if result == ValidationResult.PASS:
                summary["pass"] += 1
            elif result == ValidationResult.FAIL:
                summary["fail"] += 1
            elif result == ValidationResult.WARNING:
                summary["warning"] += 1
        
        return summary
    
    def is_data_valid(self, results: Dict[str, ValidationResult]) -> bool:
        """判断数据是否有效（没有失败项）"""
        return all(result != ValidationResult.FAIL for result in results.values())


class NPBDataValidator:
    """NPB数据验证器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def validate_port_stability(
        self,
        port_data: Dict[str, Any],
        threshold: float = 1000.0,
        error_threshold: int = 0
    ) -> Dict[str, bool]:
        """验证端口稳定性"""
        results = {
            "tx_stable": False,
            "rx_stable": False,
            "error_acceptable": False,
            "overall_stable": False
        }
        
        try:
            # 检查发送速率
            tx_rate = port_data.get("tx_rate", 0)
            results["tx_stable"] = tx_rate >= threshold
            
            # 检查接收速率
            rx_rate = port_data.get("rx_rate", 0)
            results["rx_stable"] = rx_rate >= threshold
            
            # 检查错误数
            tx_errors = port_data.get("tx_errors", 0)
            rx_errors = port_data.get("rx_errors", 0)
            results["error_acceptable"] = (tx_errors <= error_threshold and 
                                         rx_errors <= error_threshold)
            
            # 总体稳定性
            results["overall_stable"] = (results["tx_stable"] and 
                                       results["rx_stable"] and 
                                       results["error_acceptable"])
            
        except Exception as e:
            self.logger.error(f"NPB端口稳定性验证失败: {str(e)}")
        
        return results
    
    def validate_multiple_ports(
        self,
        ports_data: Dict[str, Dict[str, Any]],
        threshold: float = 1000.0,
        error_threshold: int = 0
    ) -> Dict[str, Dict[str, bool]]:
        """验证多个端口的稳定性"""
        results = {}
        
        for port_name, port_data in ports_data.items():
            results[port_name] = self.validate_port_stability(
                port_data, threshold, error_threshold
            )
        
        return results
    
    def get_unstable_ports(
        self,
        validation_results: Dict[str, Dict[str, bool]]
    ) -> List[str]:
        """获取不稳定的端口列表"""
        unstable_ports = []
        
        for port_name, results in validation_results.items():
            if not results.get("overall_stable", False):
                unstable_ports.append(port_name)
        
        return unstable_ports


# 全局验证器实例
_data_validator = DataValidator()
_npb_validator = NPBDataValidator()


def get_data_validator() -> DataValidator:
    """获取数据验证器实例"""
    return _data_validator


def get_npb_validator() -> NPBDataValidator:
    """获取NPB验证器实例"""
    return _npb_validator
