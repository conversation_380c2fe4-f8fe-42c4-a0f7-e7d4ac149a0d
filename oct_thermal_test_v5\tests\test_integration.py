#!/usr/bin/env python3
"""
集成测试脚本
"""

import sys
import os
import unittest
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent.parent
src_dir = current_dir / "src"
sys.path.insert(0, str(src_dir))

from src.app.config_manager import ConfigManager
from src.core.device_manager import Device<PERSON>anager
from src.core.test_controller import TestController
from src.core.data_processor import DataProcessor
from src.models.device_config import DeviceConfig, ConnectionConfig, SlotConfig, ConnectionType, DeviceType
from src.models.module_info import ModuleTypeConfig, ValidationRule
from src.utils.validators import get_data_validator, get_npb_validator
from src.utils.excel_handler import ExcelHandler


class TestConfigManager(unittest.TestCase):
    """配置管理器测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.config_manager = ConfigManager("test_config")
    
    def test_app_config(self):
        """测试应用程序配置"""
        # 设置配置
        self.config_manager.set_app_config("test_key", "test_value")
        
        # 获取配置
        value = self.config_manager.get_app_config("test_key")
        self.assertEqual(value, "test_value")
        
        # 测试嵌套配置
        self.config_manager.set_app_config("nested.key", "nested_value")
        nested_value = self.config_manager.get_app_config("nested.key")
        self.assertEqual(nested_value, "nested_value")
    
    def test_device_config(self):
        """测试设备配置"""
        # 创建设备配置
        device_config = DeviceConfig(
            device_id="test_device",
            device_name="测试设备",
            device_type=DeviceType.OCT,
            connection=ConnectionConfig(
                connection_type=ConnectionType.SSH,
                host="*************",
                port=22,
                username="admin",
                password="Admin_123"
            ),
            slots=[
                SlotConfig(
                    slot_id="1/1/1",
                    slot_name="测试槽位",
                    npb_ports=["CE2", "CE3"],
                    module_type="cfp2_dco"
                )
            ]
        )
        
        # 保存配置
        self.config_manager.set_device_config("test_device", device_config.to_dict())
        
        # 获取配置
        loaded_config = self.config_manager.get_device_config("test_device")
        self.assertIsNotNone(loaded_config)
        self.assertEqual(loaded_config["device_id"], "test_device")
    
    def tearDown(self):
        """清理测试环境"""
        # 清理测试配置目录
        import shutil
        test_config_dir = Path("test_config")
        if test_config_dir.exists():
            shutil.rmtree(test_config_dir)


class TestDataValidator(unittest.TestCase):
    """数据验证器测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.validator = get_data_validator()
    
    def test_range_validation(self):
        """测试范围验证"""
        rule = ValidationRule(
            field_name="temperature",
            min_value=-10.0,
            max_value=85.0,
            rule_type="range"
        )
        
        # 正常值
        result = self.validator.validate_value(25.0, rule)
        self.assertEqual(result.value, "pass")
        
        # 超出范围
        result = self.validator.validate_value(100.0, rule)
        self.assertEqual(result.value, "fail")
        
        result = self.validator.validate_value(-20.0, rule)
        self.assertEqual(result.value, "fail")
    
    def test_exact_validation(self):
        """测试精确值验证"""
        rule = ValidationRule(
            field_name="status",
            expected_value="active",
            rule_type="exact"
        )
        
        # 匹配值
        result = self.validator.validate_value("active", rule)
        self.assertEqual(result.value, "pass")
        
        # 不匹配值
        result = self.validator.validate_value("inactive", rule)
        self.assertEqual(result.value, "fail")
    
    def test_data_dict_validation(self):
        """测试数据字典验证"""
        rules = [
            ValidationRule(
                field_name="temperature",
                min_value=-10.0,
                max_value=85.0,
                rule_type="range"
            ),
            ValidationRule(
                field_name="voltage",
                min_value=3.0,
                max_value=3.6,
                rule_type="range"
            )
        ]
        
        data = {
            "temperature": 25.0,
            "voltage": 3.3
        }
        
        results = self.validator.validate_data_dict(data, rules)
        
        self.assertEqual(len(results), 2)
        self.assertEqual(results["temperature"].value, "pass")
        self.assertEqual(results["voltage"].value, "pass")


class TestNPBValidator(unittest.TestCase):
    """NPB验证器测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.validator = get_npb_validator()
    
    def test_port_stability(self):
        """测试端口稳定性验证"""
        # 稳定的端口数据
        stable_data = {
            "tx_rate": 2000.0,
            "rx_rate": 2000.0,
            "tx_errors": 0,
            "rx_errors": 0
        }
        
        results = self.validator.validate_port_stability(stable_data, 1000.0, 0)
        self.assertTrue(results["overall_stable"])
        
        # 不稳定的端口数据
        unstable_data = {
            "tx_rate": 500.0,  # 低于阈值
            "rx_rate": 2000.0,
            "tx_errors": 0,
            "rx_errors": 0
        }
        
        results = self.validator.validate_port_stability(unstable_data, 1000.0, 0)
        self.assertFalse(results["overall_stable"])


class TestExcelHandler(unittest.TestCase):
    """Excel处理器测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.excel_handler = ExcelHandler("test_output")
    
    def test_create_module_report(self):
        """测试创建模块报告"""
        data_list = [
            {
                "timestamp": "2023-01-01 10:00:00",
                "temperature": 25.0,
                "voltage": 3.3,
                "validation_passed": True
            },
            {
                "timestamp": "2023-01-01 10:01:00",
                "temperature": 26.0,
                "voltage": 3.2,
                "validation_passed": True
            }
        ]
        
        error_list = []
        
        file_path = self.excel_handler.create_module_report(
            "test_module",
            data_list,
            error_list,
            "通过"
        )
        
        self.assertTrue(Path(file_path).exists())
    
    def test_export_config_template(self):
        """测试导出配置模板"""
        file_path = self.excel_handler.export_config_template("test_template.xlsx")
        self.assertTrue(Path(file_path).exists())
    
    def tearDown(self):
        """清理测试环境"""
        # 清理测试输出目录
        import shutil
        test_output_dir = Path("test_output")
        if test_output_dir.exists():
            shutil.rmtree(test_output_dir)


class TestDeviceManager(unittest.TestCase):
    """设备管理器测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.config_manager = ConfigManager("test_config")
        self.device_manager = DeviceManager(self.config_manager)
    
    def test_add_device_config(self):
        """测试添加设备配置"""
        device_config = DeviceConfig(
            device_id="test_oct",
            device_name="测试OCT设备",
            device_type=DeviceType.OCT,
            connection=ConnectionConfig(
                connection_type=ConnectionType.SSH,
                host="*************"
            )
        )
        
        self.device_manager.add_device_config(device_config)
        
        # 验证设备是否添加
        loaded_config = self.device_manager.get_device_config("test_oct")
        self.assertIsNotNone(loaded_config)
        self.assertEqual(loaded_config.device_id, "test_oct")
    
    def tearDown(self):
        """清理测试环境"""
        self.device_manager.cleanup()
        
        # 清理测试配置目录
        import shutil
        test_config_dir = Path("test_config")
        if test_config_dir.exists():
            shutil.rmtree(test_config_dir)


class TestDataProcessor(unittest.TestCase):
    """数据处理器测试"""
    
    def setUp(self):
        """设置测试环境"""
        self.config_manager = ConfigManager("test_config")
        
        # 添加模块类型配置
        module_config = {
            "module_type": "test_module",
            "display_name": "测试模块",
            "data_fields": ["temperature", "voltage"],
            "validation_rules": [
                {
                    "field_name": "temperature",
                    "min_value": -10.0,
                    "max_value": 85.0,
                    "rule_type": "range",
                    "enabled": True,
                    "description": "温度范围检查"
                }
            ],
            "enabled": True
        }
        
        self.config_manager.set_module_type_config("test_module", module_config)
        self.data_processor = DataProcessor(self.config_manager)
    
    def test_process_module_data(self):
        """测试处理模块数据"""
        raw_data = {
            "module_id": "test_device_1/1/1",
            "device_id": "test_device",
            "slot_id": "1/1/1",
            "data_type": "test_module",
            "temperature": 25.0,
            "voltage": 3.3
        }
        
        # 处理数据
        self.data_processor.process_module_data("test_device_1/1/1", raw_data)
        
        # 验证统计信息
        stats = self.data_processor.get_module_statistics("test_device_1/1/1")
        self.assertEqual(stats["total_data_points"], 1)
    
    def tearDown(self):
        """清理测试环境"""
        self.data_processor.cleanup()
        
        # 清理测试配置目录
        import shutil
        test_config_dir = Path("test_config")
        if test_config_dir.exists():
            shutil.rmtree(test_config_dir)


def run_integration_tests():
    """运行集成测试"""
    print("开始运行集成测试...")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_suite.addTest(unittest.makeSuite(TestConfigManager))
    test_suite.addTest(unittest.makeSuite(TestDataValidator))
    test_suite.addTest(unittest.makeSuite(TestNPBValidator))
    test_suite.addTest(unittest.makeSuite(TestExcelHandler))
    test_suite.addTest(unittest.makeSuite(TestDeviceManager))
    test_suite.addTest(unittest.makeSuite(TestDataProcessor))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n所有集成测试通过！")
        return 0
    else:
        print(f"\n测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        return 1


if __name__ == "__main__":
    sys.exit(run_integration_tests())
