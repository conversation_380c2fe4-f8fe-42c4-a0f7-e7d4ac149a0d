"""
测试数据模型
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional
from datetime import datetime
from enum import Enum

from .module_info import ValidationResult


class TestStatus(Enum):
    """测试状态枚举"""
    NOT_STARTED = "not_started"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    ERROR = "error"
    STOPPED = "stopped"


@dataclass
class TestDataPoint:
    """测试数据点"""
    timestamp: datetime
    module_id: str  # 模块唯一标识
    data: Dict[str, Any]  # 测试数据
    validation_results: Dict[str, ValidationResult] = field(default_factory=dict)  # 验证结果
    npb_send_errors: Dict[str, int] = field(default_factory=dict)  # NPB发送错误数
    npb_receive_errors: Dict[str, int] = field(default_factory=dict)  # NPB接收错误数
    is_valid: bool = True  # 是否通过验证
    
    def __post_init__(self):
        # 检查是否所有验证都通过
        self.is_valid = all(
            result == ValidationResult.PASS 
            for result in self.validation_results.values()
        ) and all(
            errors == 0 
            for errors in list(self.npb_send_errors.values()) + list(self.npb_receive_errors.values())
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result_dict = {
            "timestamp": self.timestamp.isoformat(),
            "module_id": self.module_id,
            "is_valid": self.is_valid,
            **self.data
        }
        
        # 添加验证结果
        for field, result in self.validation_results.items():
            result_dict[f"{field}_validation"] = result.value
        
        # 添加NPB错误数
        for port, errors in self.npb_send_errors.items():
            result_dict[f"npb_send_errors_{port}"] = errors
        
        for port, errors in self.npb_receive_errors.items():
            result_dict[f"npb_receive_errors_{port}"] = errors
        
        return result_dict
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TestDataPoint':
        """从字典创建"""
        timestamp = datetime.fromisoformat(data["timestamp"])
        module_id = data["module_id"]
        is_valid = data.get("is_valid", True)
        
        # 提取测试数据
        test_data = {}
        validation_results = {}
        npb_send_errors = {}
        npb_receive_errors = {}
        
        for key, value in data.items():
            if key.endswith("_validation"):
                field_name = key[:-11]  # 移除 "_validation" 后缀
                validation_results[field_name] = ValidationResult(value)
            elif key.startswith("npb_send_errors_"):
                port = key[16:]  # 移除 "npb_send_errors_" 前缀
                npb_send_errors[port] = value
            elif key.startswith("npb_receive_errors_"):
                port = key[19:]  # 移除 "npb_receive_errors_" 前缀
                npb_receive_errors[port] = value
            elif key not in ["timestamp", "module_id", "is_valid"]:
                test_data[key] = value
        
        return cls(
            timestamp=timestamp,
            module_id=module_id,
            data=test_data,
            validation_results=validation_results,
            npb_send_errors=npb_send_errors,
            npb_receive_errors=npb_receive_errors,
            is_valid=is_valid
        )


@dataclass
class TestSession:
    """测试会话"""
    session_id: str  # 会话ID
    start_time: datetime
    end_time: Optional[datetime] = None
    status: TestStatus = TestStatus.NOT_STARTED
    module_ids: List[str] = field(default_factory=list)  # 参与测试的模块
    data_points: List[TestDataPoint] = field(default_factory=list)  # 测试数据点
    config: Dict[str, Any] = field(default_factory=dict)  # 测试配置
    results: Dict[str, Any] = field(default_factory=dict)  # 测试结果
    
    def add_data_point(self, data_point: TestDataPoint):
        """添加数据点"""
        self.data_points.append(data_point)
        
        # 更新模块列表
        if data_point.module_id not in self.module_ids:
            self.module_ids.append(data_point.module_id)
    
    def get_module_data(self, module_id: str) -> List[TestDataPoint]:
        """获取指定模块的数据"""
        return [dp for dp in self.data_points if dp.module_id == module_id]
    
    def get_latest_data(self, module_id: str) -> Optional[TestDataPoint]:
        """获取指定模块的最新数据"""
        module_data = self.get_module_data(module_id)
        return module_data[-1] if module_data else None
    
    def get_failed_data_points(self) -> List[TestDataPoint]:
        """获取失败的数据点"""
        return [dp for dp in self.data_points if not dp.is_valid]
    
    def get_module_status(self, module_id: str) -> TestStatus:
        """获取模块测试状态"""
        module_data = self.get_module_data(module_id)
        if not module_data:
            return TestStatus.NOT_STARTED
        
        # 检查是否有失败的数据点
        failed_points = [dp for dp in module_data if not dp.is_valid]
        if failed_points:
            return TestStatus.FAILED
        
        # 如果测试还在进行中
        if self.status == TestStatus.RUNNING:
            return TestStatus.RUNNING
        
        return TestStatus.PASSED
    
    def finish_test(self):
        """结束测试"""
        self.end_time = datetime.now()
        self.status = TestStatus.PASSED
        
        # 检查是否有模块失败
        for module_id in self.module_ids:
            if self.get_module_status(module_id) == TestStatus.FAILED:
                self.status = TestStatus.FAILED
                break
        
        # 生成测试结果统计
        self.results = {
            "total_modules": len(self.module_ids),
            "passed_modules": len([
                mid for mid in self.module_ids 
                if self.get_module_status(mid) == TestStatus.PASSED
            ]),
            "failed_modules": len([
                mid for mid in self.module_ids 
                if self.get_module_status(mid) == TestStatus.FAILED
            ]),
            "total_data_points": len(self.data_points),
            "failed_data_points": len(self.get_failed_data_points()),
            "duration_seconds": (self.end_time - self.start_time).total_seconds() if self.end_time else 0
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "session_id": self.session_id,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "status": self.status.value,
            "module_ids": self.module_ids,
            "data_points": [dp.to_dict() for dp in self.data_points],
            "config": self.config,
            "results": self.results
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TestSession':
        """从字典创建"""
        start_time = datetime.fromisoformat(data["start_time"])
        end_time = None
        if data.get("end_time"):
            end_time = datetime.fromisoformat(data["end_time"])
        
        data_points = [
            TestDataPoint.from_dict(dp_data)
            for dp_data in data.get("data_points", [])
        ]
        
        return cls(
            session_id=data["session_id"],
            start_time=start_time,
            end_time=end_time,
            status=TestStatus(data.get("status", "not_started")),
            module_ids=data.get("module_ids", []),
            data_points=data_points,
            config=data.get("config", {}),
            results=data.get("results", {})
        )
