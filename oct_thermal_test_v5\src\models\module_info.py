"""
模块信息数据模型
"""

from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from enum import Enum
from datetime import datetime


class ModuleStatus(Enum):
    """模块状态枚举"""
    UNKNOWN = "unknown"
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    TESTING = "testing"
    PASSED = "passed"
    FAILED = "failed"
    ERROR = "error"


class ValidationResult(Enum):
    """验证结果枚举"""
    PASS = "pass"
    FAIL = "fail"
    WARNING = "warning"


@dataclass
class ValidationRule:
    """验证规则"""
    field_name: str  # 字段名称
    min_value: Optional[float] = None  # 最小值
    max_value: Optional[float] = None  # 最大值
    expected_value: Optional[Any] = None  # 期望值
    tolerance: Optional[float] = None  # 容差
    rule_type: str = "range"  # 规则类型: range, exact, increase
    enabled: bool = True
    description: str = ""
    
    def validate(self, value: Any) -> ValidationResult:
        """验证值"""
        if not self.enabled:
            return ValidationResult.PASS
        
        try:
            if self.rule_type == "range":
                if self.min_value is not None and value < self.min_value:
                    return ValidationResult.FAIL
                if self.max_value is not None and value > self.max_value:
                    return ValidationResult.FAIL
            elif self.rule_type == "exact":
                if self.expected_value is not None and value != self.expected_value:
                    return ValidationResult.FAIL
            elif self.rule_type == "increase":
                # 递增检查需要历史数据，这里只做基本检查
                if self.expected_value is not None and value != self.expected_value:
                    return ValidationResult.FAIL
            
            return ValidationResult.PASS
            
        except Exception:
            return ValidationResult.ERROR
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "field_name": self.field_name,
            "min_value": self.min_value,
            "max_value": self.max_value,
            "expected_value": self.expected_value,
            "tolerance": self.tolerance,
            "rule_type": self.rule_type,
            "enabled": self.enabled,
            "description": self.description
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ValidationRule':
        """从字典创建"""
        return cls(
            field_name=data["field_name"],
            min_value=data.get("min_value"),
            max_value=data.get("max_value"),
            expected_value=data.get("expected_value"),
            tolerance=data.get("tolerance"),
            rule_type=data.get("rule_type", "range"),
            enabled=data.get("enabled", True),
            description=data.get("description", "")
        )


@dataclass
class ModuleTypeConfig:
    """模块类型配置"""
    module_type: str  # 模块类型名称
    display_name: str  # 显示名称
    data_fields: List[str] = field(default_factory=list)  # 数据字段
    validation_rules: List[ValidationRule] = field(default_factory=list)  # 验证规则
    pre_actions: List[str] = field(default_factory=list)  # 前置动作
    post_actions: List[str] = field(default_factory=list)  # 后置动作
    enabled: bool = True
    description: str = ""
    
    def get_validation_rule(self, field_name: str) -> Optional[ValidationRule]:
        """获取字段的验证规则"""
        for rule in self.validation_rules:
            if rule.field_name == field_name:
                return rule
        return None
    
    def validate_data(self, data: Dict[str, Any]) -> Dict[str, ValidationResult]:
        """验证数据"""
        results = {}
        for rule in self.validation_rules:
            if rule.field_name in data:
                results[rule.field_name] = rule.validate(data[rule.field_name])
            else:
                results[rule.field_name] = ValidationResult.WARNING
        return results
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "module_type": self.module_type,
            "display_name": self.display_name,
            "data_fields": self.data_fields,
            "validation_rules": [rule.to_dict() for rule in self.validation_rules],
            "pre_actions": self.pre_actions,
            "post_actions": self.post_actions,
            "enabled": self.enabled,
            "description": self.description
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModuleTypeConfig':
        """从字典创建"""
        validation_rules = [
            ValidationRule.from_dict(rule_data)
            for rule_data in data.get("validation_rules", [])
        ]
        
        return cls(
            module_type=data["module_type"],
            display_name=data.get("display_name", data["module_type"]),
            data_fields=data.get("data_fields", []),
            validation_rules=validation_rules,
            pre_actions=data.get("pre_actions", []),
            post_actions=data.get("post_actions", []),
            enabled=data.get("enabled", True),
            description=data.get("description", "")
        )


@dataclass
class ModuleInfo:
    """模块信息"""
    device_id: str  # 设备ID
    slot_id: str  # 槽位ID
    module_type: str  # 模块类型
    serial_number: str = ""  # 序列号
    model_number: str = ""  # 型号
    status: ModuleStatus = ModuleStatus.UNKNOWN
    last_update: Optional[datetime] = None
    npb_ports: List[str] = field(default_factory=list)  # 绑定的NPB端口
    
    def __post_init__(self):
        if self.last_update is None:
            self.last_update = datetime.now()
    
    @property
    def unique_id(self) -> str:
        """唯一标识符"""
        return f"{self.device_id}_{self.slot_id}"
    
    def update_status(self, status: ModuleStatus):
        """更新状态"""
        self.status = status
        self.last_update = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "device_id": self.device_id,
            "slot_id": self.slot_id,
            "module_type": self.module_type,
            "serial_number": self.serial_number,
            "model_number": self.model_number,
            "status": self.status.value,
            "last_update": self.last_update.isoformat() if self.last_update else None,
            "npb_ports": self.npb_ports
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModuleInfo':
        """从字典创建"""
        last_update = None
        if data.get("last_update"):
            last_update = datetime.fromisoformat(data["last_update"])
        
        return cls(
            device_id=data["device_id"],
            slot_id=data["slot_id"],
            module_type=data.get("module_type", ""),
            serial_number=data.get("serial_number", ""),
            model_number=data.get("model_number", ""),
            status=ModuleStatus(data.get("status", "unknown")),
            last_update=last_update,
            npb_ports=data.get("npb_ports", [])
        )
