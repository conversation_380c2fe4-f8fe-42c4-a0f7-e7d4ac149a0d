"""
数据处理器
"""

import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from PySide6.QtCore import QObject, Signal, QTimer

from src.app.config_manager import ConfigManager
from src.models.test_data import TestSession, TestDataPoint, TestStatus
from src.models.module_info import ModuleTypeConfig, ValidationResult
from src.utils.excel_handler import ExcelHandler
from src.utils.validators import get_data_validator, get_npb_validator
from src.utils.logger import get_logger
from src.signals.app_signals import get_app_signals


class DataProcessor(QObject):
    """数据处理器"""
    
    # 数据处理信号
    data_processed = Signal(str, dict)        # 模块ID, 处理后数据
    validation_completed = Signal(str, dict)  # 模块ID, 验证结果
    excel_exported = Signal(str, str)         # 文件路径, 模块ID
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        self.config_manager = config_manager
        self.logger = get_logger(__name__)
        
        # 工具实例
        self.excel_handler = ExcelHandler(
            self.config_manager.get_app_config("output_dir", "output")
        )
        self.data_validator = get_data_validator()
        self.npb_validator = get_npb_validator()
        
        # 数据缓存
        self._module_data_history: Dict[str, List[Dict[str, Any]]] = {}
        self._module_error_history: Dict[str, List[Dict[str, Any]]] = {}
        self._last_validation_results: Dict[str, Dict[str, ValidationResult]] = {}
        
        # 自动保存定时器
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self._auto_save_data)
        
        # 信号连接
        self.app_signals = get_app_signals()
        self._connect_signals()
        
        # 启动自动保存
        self._start_auto_save()
        
        self.logger.info("数据处理器初始化完成")
    
    def _connect_signals(self):
        """连接信号"""
        self.app_signals.module_data_updated.connect(self.process_module_data)
    
    def _start_auto_save(self):
        """启动自动保存"""
        auto_save = self.config_manager.get_app_config("excel_settings.auto_save", True)
        if auto_save:
            interval = self.config_manager.get_app_config("excel_settings.save_interval", 60)
            self.auto_save_timer.start(interval * 1000)  # 转换为毫秒
            self.logger.info(f"自动保存已启用，间隔: {interval}秒")
    
    def process_module_data(self, module_id: str, raw_data: Dict[str, Any]):
        """处理模块数据"""
        try:
            # 获取模块类型配置
            module_type = self._extract_module_type(module_id, raw_data)
            module_config = self.config_manager.get_module_type_config(module_type)
            
            if not module_config:
                self.logger.warning(f"未找到模块类型配置: {module_type}")
                return
            
            # 数据清洗和转换
            cleaned_data = self._clean_data(raw_data, module_config)
            
            # 数据验证
            validation_results = self._validate_data(module_id, cleaned_data, module_config)
            
            # 处理验证结果
            processed_data = self._process_validation_results(
                module_id, cleaned_data, validation_results
            )
            
            # 更新历史数据
            self._update_data_history(module_id, processed_data, validation_results)
            
            # 发送处理完成信号
            self.data_processed.emit(module_id, processed_data)
            self.validation_completed.emit(module_id, validation_results)
            
            # 检查是否需要立即保存
            if not validation_results or any(r == ValidationResult.FAIL for r in validation_results.values()):
                self._save_module_data_immediately(module_id)
            
        except Exception as e:
            self.logger.error(f"处理模块数据失败: {module_id}, 错误: {str(e)}")
    
    def _extract_module_type(self, module_id: str, data: Dict[str, Any]) -> str:
        """提取模块类型"""
        # 从模块ID中提取设备ID
        if "_" in module_id:
            device_id = module_id.split("_")[0]
            device_config = self.config_manager.get_device_config(device_id)
            
            if device_config:
                # 从设备配置中获取模块类型
                slots = device_config.get("slots", [])
                for slot in slots:
                    if slot.get("slot_id") in module_id:
                        return slot.get("module_type", "default")
        
        # 从数据中推断模块类型
        if "data_type" in data:
            return data["data_type"]
        
        # 默认类型
        return "default"
    
    def _clean_data(self, raw_data: Dict[str, Any], module_config: Dict[str, Any]) -> Dict[str, Any]:
        """清洗数据"""
        cleaned_data = {}
        data_fields = module_config.get("data_fields", [])
        
        for field in data_fields:
            if field in raw_data:
                value = raw_data[field]
                
                # 数据清洗
                if isinstance(value, str):
                    value = value.strip()
                    
                    # 处理特殊值
                    if value.lower() in ['n/a', 'null', 'none', '--', '']:
                        value = None
                    elif value.lower() in ['true', 'yes', 'on']:
                        value = True
                    elif value.lower() in ['false', 'no', 'off']:
                        value = False
                
                cleaned_data[field] = value
            else:
                cleaned_data[field] = None
        
        # 添加元数据
        cleaned_data.update({
            "timestamp": datetime.now().isoformat(),
            "module_id": raw_data.get("module_id", ""),
            "device_id": raw_data.get("device_id", ""),
            "slot_id": raw_data.get("slot_id", "")
        })
        
        return cleaned_data
    
    def _validate_data(
        self,
        module_id: str,
        data: Dict[str, Any],
        module_config: Dict[str, Any]
    ) -> Dict[str, ValidationResult]:
        """验证数据"""
        try:
            # 获取验证规则
            validation_rules = []
            rules_config = module_config.get("validation_rules", [])
            
            for rule_config in rules_config:
                from src.models.module_info import ValidationRule
                rule = ValidationRule.from_dict(rule_config)
                validation_rules.append(rule)
            
            # 获取历史数据用于递增验证
            previous_data = None
            if module_id in self._module_data_history:
                history = self._module_data_history[module_id]
                if history:
                    previous_data = history[-1]
            
            # 执行验证
            results = self.data_validator.validate_data_dict(
                data, validation_rules, previous_data
            )
            
            # 保存验证结果
            self._last_validation_results[module_id] = results
            
            return results
            
        except Exception as e:
            self.logger.error(f"数据验证失败: {module_id}, 错误: {str(e)}")
            return {}
    
    def _process_validation_results(
        self,
        module_id: str,
        data: Dict[str, Any],
        validation_results: Dict[str, ValidationResult]
    ) -> Dict[str, Any]:
        """处理验证结果"""
        processed_data = data.copy()
        
        # 添加验证结果
        for field, result in validation_results.items():
            processed_data[f"{field}_validation"] = result.value
        
        # 添加总体验证状态
        is_valid = self.data_validator.is_data_valid(validation_results)
        processed_data["validation_passed"] = is_valid
        
        # 添加验证汇总
        summary = self.data_validator.get_validation_summary(validation_results)
        processed_data["validation_summary"] = summary
        
        return processed_data
    
    def _update_data_history(
        self,
        module_id: str,
        data: Dict[str, Any],
        validation_results: Dict[str, ValidationResult]
    ):
        """更新数据历史"""
        # 更新数据历史
        if module_id not in self._module_data_history:
            self._module_data_history[module_id] = []
        
        self._module_data_history[module_id].append(data)
        
        # 限制历史数据长度
        max_history = self.config_manager.get_app_config("excel_settings.max_rows_per_sheet", 10000)
        if len(self._module_data_history[module_id]) > max_history:
            self._module_data_history[module_id] = self._module_data_history[module_id][-max_history:]
        
        # 更新错误历史
        if not self.data_validator.is_data_valid(validation_results):
            if module_id not in self._module_error_history:
                self._module_error_history[module_id] = []
            
            error_data = data.copy()
            error_data["error_fields"] = [
                field for field, result in validation_results.items()
                if result == ValidationResult.FAIL
            ]
            
            self._module_error_history[module_id].append(error_data)
    
    def _save_module_data_immediately(self, module_id: str):
        """立即保存模块数据"""
        try:
            if module_id not in self._module_data_history:
                return
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"module_data_{module_id}_{timestamp}.xlsx"
            
            data_list = self._module_data_history[module_id]
            error_list = self._module_error_history.get(module_id, [])
            
            # 判断测试结果
            recent_data = data_list[-10:] if len(data_list) >= 10 else data_list
            failed_count = sum(1 for d in recent_data if not d.get("validation_passed", True))
            result = "失败" if failed_count > len(recent_data) * 0.1 else "通过"
            
            file_path = self.excel_handler.create_module_report(
                module_id, data_list, error_list, result, filename
            )
            
            self.excel_exported.emit(file_path, module_id)
            self.logger.info(f"模块数据已立即保存: {module_id}")
            
        except Exception as e:
            self.logger.error(f"立即保存模块数据失败: {module_id}, 错误: {str(e)}")
    
    def _auto_save_data(self):
        """自动保存数据"""
        try:
            if not self._module_data_history:
                return
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"auto_save_{timestamp}.xlsx"
            
            # 准备汇总数据
            summary = {
                "session_id": "auto_save",
                "save_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "total_modules": len(self._module_data_history),
                "total_data_points": sum(len(data) for data in self._module_data_history.values()),
                "total_error_points": sum(len(errors) for errors in self._module_error_history.values())
            }
            
            file_path = self.excel_handler.create_test_report(
                "auto_save",
                self._module_data_history,
                self._module_error_history,
                summary,
                filename
            )
            
            self.logger.info(f"数据已自动保存: {file_path}")
            
        except Exception as e:
            self.logger.error(f"自动保存数据失败: {str(e)}")
    
    def export_session_report(self, session: TestSession) -> str:
        """导出会话报告"""
        try:
            # 准备数据
            module_data = {}
            error_data = {}
            
            for module_id in session.module_ids:
                # 获取模块的所有数据点
                module_points = session.get_module_data(module_id)
                
                data_list = []
                error_list = []
                
                for point in module_points:
                    data_dict = point.to_dict()
                    data_list.append(data_dict)
                    
                    if not point.is_valid:
                        error_list.append(data_dict)
                
                if data_list:
                    module_data[module_id] = data_list
                if error_list:
                    error_data[module_id] = error_list
            
            # 准备汇总信息
            summary = {
                "session_id": session.session_id,
                "start_time": session.start_time.strftime("%Y-%m-%d %H:%M:%S"),
                "end_time": session.end_time.strftime("%Y-%m-%d %H:%M:%S") if session.end_time else "",
                "duration": str(session.end_time - session.start_time) if session.end_time else "",
                "overall_result": session.status == TestStatus.PASSED,
                **session.results
            }
            
            # 生成文件名
            timestamp = session.start_time.strftime("%Y%m%d_%H%M%S")
            filename = f"test_report_{session.session_id}_{timestamp}.xlsx"
            
            # 创建报告
            file_path = self.excel_handler.create_test_report(
                session.session_id,
                module_data,
                error_data,
                summary,
                filename
            )
            
            self.excel_exported.emit(file_path, session.session_id)
            self.logger.info(f"会话报告已导出: {file_path}")
            return file_path
            
        except Exception as e:
            self.logger.error(f"导出会话报告失败: {session.session_id}, 错误: {str(e)}")
            raise
    
    def process_npb_data(self, npb_device_id: str, ports_data: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """处理NPB数据"""
        try:
            # 验证端口稳定性
            threshold = self.config_manager.get_app_config("npb_settings.stability_threshold", 1000.0)
            error_threshold = self.config_manager.get_app_config("npb_settings.error_threshold", 0)
            
            validation_results = self.npb_validator.validate_multiple_ports(
                ports_data, threshold, error_threshold
            )
            
            # 获取不稳定端口
            unstable_ports = self.npb_validator.get_unstable_ports(validation_results)
            
            # 准备结果
            result = {
                "npb_device_id": npb_device_id,
                "timestamp": datetime.now().isoformat(),
                "ports_data": ports_data,
                "validation_results": validation_results,
                "unstable_ports": unstable_ports,
                "overall_stable": len(unstable_ports) == 0
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"处理NPB数据失败: {npb_device_id}, 错误: {str(e)}")
            return {}
    
    def get_module_statistics(self, module_id: str) -> Dict[str, Any]:
        """获取模块统计信息"""
        if module_id not in self._module_data_history:
            return {}
        
        data_history = self._module_data_history[module_id]
        error_history = self._module_error_history.get(module_id, [])
        
        return {
            "total_data_points": len(data_history),
            "error_data_points": len(error_history),
            "success_rate": (len(data_history) - len(error_history)) / len(data_history) if data_history else 0,
            "last_update": data_history[-1].get("timestamp") if data_history else None,
            "validation_summary": data_history[-1].get("validation_summary") if data_history else {}
        }
    
    def clear_module_history(self, module_id: str):
        """清空模块历史数据"""
        if module_id in self._module_data_history:
            del self._module_data_history[module_id]
        if module_id in self._module_error_history:
            del self._module_error_history[module_id]
        if module_id in self._last_validation_results:
            del self._last_validation_results[module_id]
        
        self.logger.info(f"模块历史数据已清空: {module_id}")
    
    def cleanup(self):
        """清理资源"""
        try:
            # 停止自动保存
            self.auto_save_timer.stop()
            
            # 执行最后一次保存
            if self._module_data_history:
                self._auto_save_data()
            
            # 清空缓存
            self._module_data_history.clear()
            self._module_error_history.clear()
            self._last_validation_results.clear()
            
            self.logger.info("数据处理器清理完成")
            
        except Exception as e:
            self.logger.error(f"数据处理器清理失败: {str(e)}")
