# OCT Thermal Test V5 - 重构版本

基于PySide6的OCT设备热循环测试上位机软件重构版本。

## 主要功能

- 支持多种OCT设备和模块类型
- 多Tab界面设计，每个Tab对应一种模块类型
- 支持SSH和串口连接OCT设备
- NPB端口流量监控和错误数清零
- 实时数据监控和Excel输出
- 挂机流程自动化控制
- 支持一对多端口绑定（一个模块对应最多4个NPB端口）

## 项目结构

```
oct_thermal_test_v5/
├── src/                    # 源代码
│   ├── app/               # 应用程序核心
│   │   ├── application.py # 主应用程序类
│   │   └── config_manager.py # 配置管理器
│   ├── core/              # 核心业务逻辑
│   │   ├── device_manager.py # 设备管理器
│   │   ├── test_controller.py # 测试流程控制器
│   │   └── data_processor.py # 数据处理器
│   ├── devices/           # 设备连接层
│   │   ├── base.py        # 设备基类
│   │   ├── oct_device.py  # OCT设备
│   │   ├── npb_device.py  # NPB设备
│   │   ├── ssh_connection.py # SSH连接
│   │   └── serial_connection.py # 串口连接
│   ├── models/            # 数据模型
│   │   ├── device_config.py # 设备配置模型
│   │   ├── module_info.py # 模块信息模型
│   │   └── test_data.py   # 测试数据模型
│   ├── ui/                # 用户界面
│   │   ├── main_window.py # 主窗口
│   │   ├── device_tab.py  # 设备Tab页面
│   │   ├── widgets/       # 自定义控件
│   │   └── dialogs/       # 对话框
│   ├── utils/             # 工具类
│   │   ├── logger.py      # 日志工具
│   │   ├── excel_handler.py # Excel处理
│   │   └── validators.py  # 数据验证
│   └── signals/           # 信号定义
│       └── app_signals.py # 应用信号
├── config/                # 配置文件
│   ├── app_config.json    # 应用配置
│   ├── device_configs/    # 设备配置目录
│   └── module_types/      # 模块类型配置
├── tests/                 # 测试文件
│   ├── test_integration.py # 集成测试
│   └── test_performance.py # 性能测试
├── logs/                  # 日志目录
├── output/                # 输出文件目录
├── main.py               # 程序入口
├── run_tests.py          # 测试运行器
└── requirements.txt      # 依赖包
```

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 检查依赖

```bash
python run_tests.py deps
```

### 3. 创建示例配置

```bash
python run_tests.py config
```

### 4. 运行程序

```bash
python main.py
```

## 测试

### 运行集成测试

```bash
python run_tests.py integration
```

### 运行性能测试

```bash
python run_tests.py performance
```

### 运行所有测试

```bash
python run_tests.py all
```

## 配置说明

### 应用程序配置 (config/app_config.json)

```json
{
  "app_name": "OCT Thermal Test V5",
  "version": "5.0.0",
  "log_level": "INFO",
  "single_loop_time": 10,
  "max_retry_count": 3,
  "connection_timeout": 30,
  "ui_settings": {
    "window_size": [1200, 800],
    "theme": "default"
  },
  "excel_settings": {
    "auto_save": true,
    "save_interval": 60,
    "max_rows_per_sheet": 10000
  },
  "test_settings": {
    "stability_check_timeout": 300,
    "clear_errors_before_test": true,
    "auto_retry_on_failure": true
  }
}
```

### 设备配置示例 (config/device_configs/oct_001.json)

```json
{
  "device_id": "oct_001",
  "device_name": "OCT设备001",
  "device_type": "oct",
  "connection": {
    "connection_type": "ssh",
    "host": "**************",
    "port": 22,
    "username": "admin",
    "password": "Admin_123",
    "timeout": 30
  },
  "slots": [
    {
      "slot_id": "1/1/1",
      "slot_name": "槽位 1/1/1",
      "max_modules": 2,
      "enabled": true,
      "npb_ports": ["CE2", "CE3", "CE4", "CE5"],
      "module_type": "cfp2_dco"
    }
  ],
  "enabled": true
}
```

### 模块类型配置示例 (config/module_types/cfp2_dco.json)

```json
{
  "module_type": "cfp2_dco",
  "display_name": "CFP2 DCO模块",
  "data_fields": [
    "sn", "mn", "Module Temperature", "Module Power Supply",
    "Network TX Laser Temp", "Current Output Power", "Current Input Power"
  ],
  "validation_rules": [
    {
      "field_name": "Module Temperature",
      "min_value": -10.0,
      "max_value": 85.0,
      "rule_type": "range",
      "enabled": true,
      "description": "模块温度范围检查"
    }
  ],
  "pre_actions": ["switch_to_slot", "check_module_presence"],
  "enabled": true
}
```

## 挂机流程

1. **连接检查**: 连接全部模块的NPB端口，检查流量是否正常
2. **错误清零**: 使用clear_error_nums方法将NPB端口错误数清零
3. **设备初始化**: 连接OCT设备并进行初始化
4. **模块检测**: 检查各槽位是否有模块存在
5. **前置动作**: 执行模块类型特定的前置准备动作
6. **数据采集**: 循环读取模块数据并进行实时验证
7. **结果输出**: 更新界面显示，保存数据到Excel文件
8. **错误处理**: 失败时显示红色状态，支持自动重试

## 架构特点

### 分层设计
- **UI层**: 基于PySide6的现代化界面
- **业务逻辑层**: 测试流程控制和数据处理
- **设备访问层**: 统一的设备连接抽象
- **数据模型层**: 类型安全的数据结构

### 设计模式
- **观察者模式**: 基于信号槽的事件通知
- **工厂模式**: 设备和连接的动态创建
- **策略模式**: 可配置的验证规则
- **单例模式**: 全局配置和日志管理

### 并发处理
- **多线程**: 测试任务在独立线程中执行
- **异步IO**: 设备通信采用异步模式
- **线程安全**: 使用信号槽确保UI更新安全

## 扩展指南

### 添加新的模块类型

1. 在 `config/module_types/` 目录创建配置文件
2. 定义数据字段和验证规则
3. 实现特定的前置/后置动作（如需要）

### 添加新的设备类型

1. 继承 `Device` 基类
2. 实现设备特定的通信协议
3. 在设备管理器中注册新类型

### 自定义验证规则

1. 在 `validators.py` 中添加新的验证方法
2. 在模块配置中引用新的规则类型
3. 支持自定义参数和错误消息

## 故障排除

### 常见问题

1. **依赖包缺失**: 运行 `python run_tests.py deps` 检查
2. **配置文件错误**: 检查JSON格式和必需字段
3. **设备连接失败**: 验证网络连接和认证信息
4. **权限问题**: 确保有写入日志和输出目录的权限

### 日志分析

- 应用日志: `logs/app_YYYYMMDD.log`
- 错误日志: `logs/error_YYYYMMDD.log`
- 设备日志: 按设备ID分别记录

### 性能优化

- 调整循环间隔时间
- 限制历史数据保存数量
- 使用批量Excel写入
- 优化验证规则复杂度
