# OCT Thermal Test V5 - 重构版本

基于PySide6的OCT设备热循环测试上位机软件重构版本。

## 主要功能

- 支持多种OCT设备和模块类型
- 多Tab界面设计，每个Tab对应一种模块类型  
- 支持SSH和串口连接OCT设备
- NPB端口流量监控和错误数清零
- 实时数据监控和Excel输出
- 挂机流程自动化控制
- 支持一对多端口绑定（一个模块对应最多4个NPB端口）

## 项目结构

```
oct_thermal_test_v5/
├── src/                    # 源代码
│   ├── app/               # 应用程序核心
│   ├── core/              # 核心业务逻辑
│   ├── devices/           # 设备连接层
│   ├── models/            # 数据模型
│   ├── ui/                # 用户界面
│   ├── utils/             # 工具类
│   └── signals/           # 信号定义
├── config/                # 配置文件
├── ui/                    # UI文件
├── tests/                 # 测试文件
├── logs/                  # 日志目录
├── output/                # 输出文件目录
└── main.py               # 程序入口
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 运行程序

```bash
python main.py
```

## 配置说明

### 设备配置
- 支持多个OCT设备配置
- 每个设备最多4个槽位，每个槽位最多2个模块
- 支持SSH和串口连接方式

### 模块类型配置
- 支持多种模块类型，使用Excel多sheet管理
- 每种模块类型对应一个Tab页面
- 支持不同模块类型的前置准备动作

### NPB端口配置
- 支持一对多端口绑定
- 至少一个端口对应一个模块
- 最多4个端口对应一个模块

## 挂机流程

1. 连接全部模块的NPB端口
2. 检查流量是否正常
3. 如果正常则开始，不正常则判定挂机失败
4. 使用clear_error_nums方法清零错误数
5. 连接OCT设备开始监控模块内部数据
6. 执行前置准备动作（如果需要）
7. 切换槽位到模块位置
8. 获取数据、解析数据、校验数据
9. 更新界面和Excel文件
10. 失败时显示红色并重试
