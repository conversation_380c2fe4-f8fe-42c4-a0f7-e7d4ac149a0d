"""
NPB设备实现
"""

import requests
import urllib3
from typing import Dict, Any, List, Optional
from .base import Device, DeviceConnection, ConnectionStatus
from src.utils.logger import get_logger

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class NPBHTTPConnection(DeviceConnection):
    """NPB HTTP连接实现"""
    
    def __init__(
        self,
        connection_id: str,
        host: str,
        username: str = "admin",
        password: str = "Admin_123",
        timeout: int = 30,
        verify_ssl: bool = False
    ):
        super().__init__(connection_id)
        self.host = host
        self.username = username
        self.password = password
        self.timeout = timeout
        self.verify_ssl = verify_ssl
        
        self.session = requests.Session()
        self.login_url = f"https://{host}/action/login"
        self.query_url = f"https://{host}/action/web_getAllFlowInfos?"
        self.clear_url = f"https://{host}/action/web_clearFlow?"
        
        self._is_logged_in = False
    
    def connect(self) -> bool:
        """建立NPB连接（登录）"""
        try:
            self._set_status(ConnectionStatus.CONNECTING)
            
            # 准备登录数据
            payload = {
                "username": self.username,
                "password": self.password,
                "language": 0
            }
            
            # 发送登录请求
            response = self.session.post(
                self.login_url,
                data=payload,
                verify=self.verify_ssl,
                timeout=self.timeout
            )
            
            # 检查登录是否成功
            if (response.status_code == 200 and 
                self.session.cookies.get_dict().get("-goahead-session-")):
                
                self._is_logged_in = True
                self._set_status(ConnectionStatus.CONNECTED)
                self.logger.info(f"NPB登录成功: {self.host}")
                return True
            else:
                error_msg = f"NPB登录失败: {self.host}, 状态码: {response.status_code}"
                self._set_status(ConnectionStatus.ERROR, error_msg)
                return False
                
        except requests.exceptions.Timeout:
            error_msg = f"NPB连接超时: {self.host}"
            self._set_status(ConnectionStatus.ERROR, error_msg)
            return False
            
        except requests.exceptions.ConnectionError as e:
            error_msg = f"NPB连接失败: {self.host}, 错误: {str(e)}"
            self._set_status(ConnectionStatus.ERROR, error_msg)
            return False
            
        except Exception as e:
            error_msg = f"NPB连接异常: {self.host}, 错误: {str(e)}"
            self._set_status(ConnectionStatus.ERROR, error_msg)
            return False
    
    def disconnect(self):
        """断开NPB连接"""
        try:
            self.session.close()
            self._is_logged_in = False
            self._set_status(ConnectionStatus.DISCONNECTED)
            self.logger.info(f"NPB连接已断开: {self.host}")
        except Exception as e:
            self.logger.error(f"断开NPB连接时发生错误: {str(e)}")
    
    def send_command(self, command: str, timeout: Optional[int] = None) -> str:
        """发送HTTP请求命令"""
        if not self.is_connected:
            raise ConnectionError("NPB连接未建立")
        
        # NPB设备使用HTTP API，这里主要用于查询和清零操作
        if command == "query_flow_info":
            return self._query_flow_info(timeout)
        elif command.startswith("clear_errors:"):
            port = command.split(":", 1)[1]
            return self._clear_port_errors(port, timeout)
        else:
            raise ValueError(f"不支持的NPB命令: {command}")
    
    def _query_flow_info(self, timeout: Optional[int] = None) -> str:
        """查询流量信息"""
        try:
            if timeout is None:
                timeout = self.timeout
            
            headers = {"Content-Type": "text/plain; charset=UTF-8"}
            body = "web_getAllFlowInfo=web_getAllFlowInfos"
            
            response = self.session.post(
                self.query_url,
                data=body,
                headers=headers,
                verify=self.verify_ssl,
                timeout=timeout
            )
            
            if response.status_code == 200:
                return response.text
            else:
                raise RuntimeError(f"查询流量信息失败，状态码: {response.status_code}")
                
        except Exception as e:
            error_msg = f"查询NPB流量信息失败: {str(e)}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)
    
    def _clear_port_errors(self, port: str, timeout: Optional[int] = None) -> str:
        """清除端口错误数"""
        try:
            if timeout is None:
                timeout = self.timeout
            
            headers = {"Content-Type": "text/plain; charset=UTF-8"}
            body = f"web_clearFlow={port}"
            
            response = self.session.post(
                self.clear_url,
                data=body,
                headers=headers,
                verify=self.verify_ssl,
                timeout=timeout
            )
            
            if response.status_code == 200:
                return response.text
            else:
                raise RuntimeError(f"清除端口错误数失败，状态码: {response.status_code}")
                
        except Exception as e:
            error_msg = f"清除NPB端口错误数失败: 端口={port}, 错误={str(e)}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)
    
    def is_alive(self) -> bool:
        """检查连接是否存活"""
        return self._is_logged_in and self.session is not None
    
    def get_connection_info(self) -> dict:
        """获取连接信息"""
        info = super().get_connection_info()
        info.update({
            "connection_type": "http",
            "host": self.host,
            "username": self.username,
            "timeout": self.timeout,
            "verify_ssl": self.verify_ssl,
            "is_logged_in": self._is_logged_in
        })
        return info


class NPBDevice(Device):
    """NPB设备实现"""
    
    def __init__(self, device_id: str, host: str, username: str = "admin", password: str = "Admin_123"):
        # 创建HTTP连接
        connection = NPBHTTPConnection(
            connection_id=f"npb_{device_id}",
            host=host,
            username=username,
            password=password
        )
        
        super().__init__(device_id, connection)
        self.host = host
        self._flow_data: List[Dict[str, Any]] = []
    
    def initialize(self) -> bool:
        """初始化NPB设备"""
        try:
            self.logger.info("正在初始化NPB设备...")
            
            # NPB设备的初始化就是登录
            if self.connection.connect():
                self.logger.info("NPB设备初始化成功")
                return True
            else:
                self.logger.error("NPB设备初始化失败")
                return False
                
        except Exception as e:
            self.logger.error(f"NPB设备初始化异常: {str(e)}")
            return False
    
    def get_device_info(self) -> Dict[str, Any]:
        """获取设备信息"""
        return {
            "device_id": self.device_id,
            "device_type": "npb",
            "host": self.host,
            "connection_type": "http"
        }
    
    def check_health(self) -> bool:
        """检查设备健康状态"""
        try:
            # 尝试查询流量信息来检查设备状态
            self.query_flow_data()
            return True
        except Exception as e:
            self.logger.error(f"NPB设备健康检查失败: {str(e)}")
            return False
    
    def query_flow_data(self) -> List[Dict[str, Any]]:
        """查询流量数据"""
        try:
            result = self.send_command("query_flow_info")
            
            # 解析JSON响应
            import json
            flow_data = json.loads(result)
            
            if isinstance(flow_data, list):
                self._flow_data = flow_data
                self.logger.info(f"获取NPB流量数据成功，端口数: {len(flow_data)}")
                return flow_data
            else:
                self.logger.error("NPB流量数据格式错误")
                return []
                
        except Exception as e:
            self.logger.error(f"查询NPB流量数据失败: {str(e)}")
            return []
    
    def get_port_data(self, port_name: str) -> Optional[Dict[str, Any]]:
        """获取指定端口的数据"""
        for port_data in self._flow_data:
            if port_data.get("port") == port_name or port_data.get("name") == port_name:
                return port_data
        return None
    
    def get_port_traffic(self, port_name: str) -> Dict[str, Any]:
        """获取端口流量信息"""
        port_data = self.get_port_data(port_name)
        if not port_data:
            return {"error": f"端口 {port_name} 未找到"}
        
        return {
            "port": port_name,
            "tx_rate": port_data.get("tx_rate", 0),
            "rx_rate": port_data.get("rx_rate", 0),
            "tx_errors": port_data.get("tx_errors", 0),
            "rx_errors": port_data.get("rx_errors", 0),
            "status": port_data.get("status", "unknown")
        }
    
    def clear_port_errors(self, port_name: str) -> bool:
        """清除端口错误数"""
        try:
            result = self.send_command(f"clear_errors:{port_name}")
            self.logger.info(f"清除端口 {port_name} 错误数成功")
            return True
        except Exception as e:
            self.logger.error(f"清除端口 {port_name} 错误数失败: {str(e)}")
            return False
    
    def clear_all_errors(self, port_names: List[str]) -> Dict[str, bool]:
        """清除多个端口的错误数"""
        results = {}
        for port_name in port_names:
            results[port_name] = self.clear_port_errors(port_name)
        return results
    
    def check_port_stability(self, port_name: str, threshold: float = 1000.0) -> bool:
        """检查端口流量稳定性"""
        port_data = self.get_port_traffic(port_name)
        
        if "error" in port_data:
            return False
        
        tx_rate = port_data.get("tx_rate", 0)
        rx_rate = port_data.get("rx_rate", 0)
        
        # 检查发送和接收速率是否都大于阈值
        return tx_rate > threshold and rx_rate > threshold
    
    def check_ports_stability(self, port_names: List[str], threshold: float = 1000.0) -> Dict[str, bool]:
        """检查多个端口的稳定性"""
        # 先更新流量数据
        self.query_flow_data()
        
        results = {}
        for port_name in port_names:
            results[port_name] = self.check_port_stability(port_name, threshold)
        
        return results
    
    def get_port_errors(self, port_name: str) -> Dict[str, int]:
        """获取端口错误数"""
        port_data = self.get_port_traffic(port_name)
        
        if "error" in port_data:
            return {"tx_errors": -1, "rx_errors": -1}
        
        return {
            "tx_errors": port_data.get("tx_errors", 0),
            "rx_errors": port_data.get("rx_errors", 0)
        }
    
    def get_all_ports_errors(self, port_names: List[str]) -> Dict[str, Dict[str, int]]:
        """获取所有端口的错误数"""
        # 先更新流量数据
        self.query_flow_data()
        
        results = {}
        for port_name in port_names:
            results[port_name] = self.get_port_errors(port_name)
        
        return results
    
    def get_status(self) -> Dict[str, Any]:
        """获取设备状态"""
        status = super().get_status()
        status.update({
            "device_type": "npb",
            "host": self.host,
            "total_ports": len(self._flow_data),
            "last_query_time": getattr(self, '_last_query_time', None)
        })
        return status
