"""
主窗口实现
"""

import sys
from typing import Dict, List, Optional
from PySide6.QtWidgets import (
    QMainWindow, QTabWidget, QVBoxLayout, QHBoxLayout, 
    QWidget, QPushButton, QLabel, QStatusBar, QMenuBar,
    QTextEdit, QSplitter, QGroupBox, QSpinBox, QProgressBar,
    QMessageBox, QApplication
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QAction, QIcon

from src.app.config_manager import ConfigManager
from src.signals.app_signals import get_app_signals, get_ui_signals
from src.utils.logger import get_logger, get_log_signals
from .device_tab import DeviceTab


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()
        self.config_manager = config_manager
        self.logger = get_logger(__name__)
        
        # 信号连接
        self.app_signals = get_app_signals()
        self.ui_signals = get_ui_signals()
        self.log_signals = get_log_signals()
        
        # Tab管理
        self.device_tabs: Dict[str, DeviceTab] = {}
        
        # UI组件
        self.tab_widget: Optional[QTabWidget] = None
        self.log_text: Optional[QTextEdit] = None
        self.status_bar: Optional[QStatusBar] = None
        self.progress_bar: Optional[QProgressBar] = None
        
        # 控制按钮
        self.start_button: Optional[QPushButton] = None
        self.stop_button: Optional[QPushButton] = None
        self.pause_button: Optional[QPushButton] = None
        
        # 定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._update_status)
        
        self._setup_ui()
        self._connect_signals()
        self._load_window_settings()
        
        self.logger.info("主窗口初始化完成")
    
    def _setup_ui(self):
        """设置UI"""
        self.setWindowTitle("OCT Thermal Test V5")
        self.setMinimumSize(1200, 800)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建菜单栏
        self._create_menu_bar()
        
        # 创建工具栏
        self._create_toolbar()
        
        # 创建主分割器
        main_splitter = QSplitter(Qt.Vertical)
        main_layout.addWidget(main_splitter)
        
        # 创建上半部分（Tab区域）
        self._create_tab_area(main_splitter)
        
        # 创建下半部分（日志区域）
        self._create_log_area(main_splitter)
        
        # 设置分割器比例
        main_splitter.setSizes([600, 200])
        
        # 创建状态栏
        self._create_status_bar()
    
    def _create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 导入配置
        import_action = QAction("导入配置(&I)", self)
        import_action.triggered.connect(self._import_config)
        file_menu.addAction(import_action)
        
        # 导出配置
        export_action = QAction("导出配置(&E)", self)
        export_action.triggered.connect(self._export_config)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 设备菜单
        device_menu = menubar.addMenu("设备(&D)")
        
        # 连接所有设备
        connect_all_action = QAction("连接所有设备(&C)", self)
        connect_all_action.triggered.connect(self._connect_all_devices)
        device_menu.addAction(connect_all_action)
        
        # 断开所有设备
        disconnect_all_action = QAction("断开所有设备(&D)", self)
        disconnect_all_action.triggered.connect(self._disconnect_all_devices)
        device_menu.addAction(disconnect_all_action)
        
        # 测试菜单
        test_menu = menubar.addMenu("测试(&T)")
        
        # 开始测试
        start_test_action = QAction("开始测试(&S)", self)
        start_test_action.triggered.connect(self._start_test)
        test_menu.addAction(start_test_action)
        
        # 停止测试
        stop_test_action = QAction("停止测试(&T)", self)
        stop_test_action.triggered.connect(self._stop_test)
        test_menu.addAction(stop_test_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)
    
    def _create_toolbar(self):
        """创建工具栏"""
        toolbar = self.addToolBar("主工具栏")
        
        # 控制按钮组
        control_group = QGroupBox("测试控制")
        control_layout = QHBoxLayout(control_group)
        
        # 开始按钮
        self.start_button = QPushButton("开始测试")
        self.start_button.clicked.connect(self._start_test)
        control_layout.addWidget(self.start_button)
        
        # 暂停按钮
        self.pause_button = QPushButton("暂停测试")
        self.pause_button.clicked.connect(self._pause_test)
        self.pause_button.setEnabled(False)
        control_layout.addWidget(self.pause_button)
        
        # 停止按钮
        self.stop_button = QPushButton("停止测试")
        self.stop_button.clicked.connect(self._stop_test)
        self.stop_button.setEnabled(False)
        control_layout.addWidget(self.stop_button)
        
        # 添加到工具栏
        toolbar.addWidget(control_group)
        
        toolbar.addSeparator()
        
        # 设置组
        settings_group = QGroupBox("测试设置")
        settings_layout = QHBoxLayout(settings_group)
        
        # 循环间隔设置
        settings_layout.addWidget(QLabel("循环间隔(秒):"))
        self.loop_interval_spin = QSpinBox()
        self.loop_interval_spin.setRange(1, 3600)
        self.loop_interval_spin.setValue(self.config_manager.get_app_config("single_loop_time", 10))
        self.loop_interval_spin.valueChanged.connect(self._on_loop_interval_changed)
        settings_layout.addWidget(self.loop_interval_spin)
        
        toolbar.addWidget(settings_group)
    
    def _create_tab_area(self, parent):
        """创建Tab区域"""
        # 创建Tab控件
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabsClosable(True)
        self.tab_widget.tabCloseRequested.connect(self._close_tab)
        self.tab_widget.currentChanged.connect(self._on_tab_changed)
        
        parent.addWidget(self.tab_widget)
        
        # 加载模块类型Tab
        self._load_module_type_tabs()
    
    def _create_log_area(self, parent):
        """创建日志区域"""
        log_group = QGroupBox("日志信息")
        log_layout = QVBoxLayout(log_group)
        
        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        parent.addWidget(log_group)
    
    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # 状态标签
        self.status_bar.showMessage("就绪")
    
    def _connect_signals(self):
        """连接信号"""
        # 应用程序信号
        self.app_signals.test_started.connect(self._on_test_started)
        self.app_signals.test_stopped.connect(self._on_test_stopped)
        self.app_signals.test_completed.connect(self._on_test_completed)
        
        # UI信号
        self.ui_signals.tab_added.connect(self._on_tab_added)
        self.ui_signals.tab_removed.connect(self._on_tab_removed)
        self.ui_signals.status_message.connect(self._on_status_message)
        self.ui_signals.status_progress.connect(self._on_status_progress)
        
        # 日志信号
        self.log_signals.log_message.connect(self._on_log_message)
        
        # 消息信号
        self.app_signals.info_message.connect(self._show_info_message)
        self.app_signals.warning_message.connect(self._show_warning_message)
        self.app_signals.error_message.connect(self._show_error_message)
    
    def _load_module_type_tabs(self):
        """加载模块类型Tab"""
        module_types = self.config_manager.get_all_module_types()
        
        for module_type in module_types:
            self._add_module_type_tab(module_type)
        
        # 如果没有模块类型，添加默认Tab
        if not module_types:
            self._add_module_type_tab("default")
    
    def _add_module_type_tab(self, module_type: str):
        """添加模块类型Tab"""
        if module_type in self.device_tabs:
            return
        
        # 获取模块类型配置
        module_config = self.config_manager.get_module_type_config(module_type)
        
        # 创建设备Tab
        device_tab = DeviceTab(module_type, module_config, self.config_manager)
        self.device_tabs[module_type] = device_tab
        
        # 添加到Tab控件
        display_name = module_config.get("display_name", module_type) if module_config else module_type
        self.tab_widget.addTab(device_tab, display_name)
        
        self.logger.info(f"添加模块类型Tab: {module_type}")
    
    def _close_tab(self, index: int):
        """关闭Tab"""
        if index < 0 or index >= self.tab_widget.count():
            return
        
        # 获取Tab名称
        widget = self.tab_widget.widget(index)
        module_type = None
        
        for mt, tab in self.device_tabs.items():
            if tab == widget:
                module_type = mt
                break
        
        if module_type:
            # 确认关闭
            reply = QMessageBox.question(
                self, "确认关闭",
                f"确定要关闭模块类型 '{module_type}' 的Tab吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.tab_widget.removeTab(index)
                del self.device_tabs[module_type]
                self.logger.info(f"关闭模块类型Tab: {module_type}")
    
    def _on_tab_changed(self, index: int):
        """Tab切换事件"""
        if index >= 0:
            widget = self.tab_widget.widget(index)
            for module_type, tab in self.device_tabs.items():
                if tab == widget:
                    self.ui_signals.tab_activated.emit(module_type)
                    break
    
    def _load_window_settings(self):
        """加载窗口设置"""
        window_size = self.config_manager.get_app_config("ui_settings.window_size", [1200, 800])
        window_position = self.config_manager.get_app_config("ui_settings.window_position", [100, 100])
        
        self.resize(window_size[0], window_size[1])
        self.move(window_position[0], window_position[1])
    
    def _save_window_settings(self):
        """保存窗口设置"""
        size = self.size()
        position = self.pos()
        
        self.config_manager.set_app_config("ui_settings.window_size", [size.width(), size.height()])
        self.config_manager.set_app_config("ui_settings.window_position", [position.x(), position.y()])
    
    # 事件处理方法
    def _import_config(self):
        """导入配置"""
        # TODO: 实现配置导入
        pass
    
    def _export_config(self):
        """导出配置"""
        # TODO: 实现配置导出
        pass
    
    def _connect_all_devices(self):
        """连接所有设备"""
        self.app_signals.info_message.emit("设备连接", "正在连接所有设备...")
    
    def _disconnect_all_devices(self):
        """断开所有设备"""
        self.app_signals.info_message.emit("设备连接", "正在断开所有设备...")
    
    def _start_test(self):
        """开始测试"""
        self.app_signals.test_started.emit("main_session")
    
    def _pause_test(self):
        """暂停测试"""
        self.app_signals.test_paused.emit("main_session")
    
    def _stop_test(self):
        """停止测试"""
        self.app_signals.test_stopped.emit("main_session")
    
    def _show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self, "关于",
            f"OCT Thermal Test V5\n"
            f"版本: {self.config_manager.get_app_config('version', '5.0.0')}\n"
            f"基于PySide6的OCT设备热循环测试上位机软件"
        )
    
    def _on_loop_interval_changed(self, value: int):
        """循环间隔变更"""
        self.config_manager.set_app_config("single_loop_time", value)
    
    # 信号处理方法
    def _on_test_started(self, session_id: str):
        """测试开始"""
        self.start_button.setEnabled(False)
        self.pause_button.setEnabled(True)
        self.stop_button.setEnabled(True)
        self.status_bar.showMessage("测试运行中...")
        self.status_timer.start(1000)  # 每秒更新状态
    
    def _on_test_stopped(self, session_id: str):
        """测试停止"""
        self.start_button.setEnabled(True)
        self.pause_button.setEnabled(False)
        self.stop_button.setEnabled(False)
        self.status_bar.showMessage("测试已停止")
        self.status_timer.stop()
    
    def _on_test_completed(self, session_id: str, success: bool):
        """测试完成"""
        self._on_test_stopped(session_id)
        status = "测试完成" if success else "测试失败"
        self.status_bar.showMessage(status)
    
    def _on_tab_added(self, tab_name: str):
        """Tab添加"""
        pass
    
    def _on_tab_removed(self, tab_name: str):
        """Tab移除"""
        pass
    
    def _on_status_message(self, message: str):
        """状态消息"""
        self.status_bar.showMessage(message)
    
    def _on_status_progress(self, value: int):
        """状态进度"""
        if value >= 0:
            self.progress_bar.setValue(value)
            self.progress_bar.setVisible(True)
        else:
            self.progress_bar.setVisible(False)
    
    def _on_log_message(self, message: str):
        """日志消息"""
        if self.log_text:
            self.log_text.append(message)
            # 自动滚动到底部
            cursor = self.log_text.textCursor()
            cursor.movePosition(cursor.End)
            self.log_text.setTextCursor(cursor)
    
    def _show_info_message(self, title: str, message: str):
        """显示信息消息"""
        QMessageBox.information(self, title, message)
    
    def _show_warning_message(self, title: str, message: str):
        """显示警告消息"""
        QMessageBox.warning(self, title, message)
    
    def _show_error_message(self, title: str, message: str):
        """显示错误消息"""
        QMessageBox.critical(self, title, message)
    
    def _update_status(self):
        """更新状态"""
        # TODO: 更新运行时状态信息
        pass
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        self._save_window_settings()
        self.app_signals.app_stopping.emit()
        event.accept()
